// Node Loader for Dynamic Plugin System
import { NodeProps } from 'reactflow';

export interface NodeDefinition {
  id: string;
  name: string;
  type: string;
  category: string;
  description: string;
  icon: string;
  version: string;
  inputs: NodeInput[];
  outputs: NodeOutput[];
  config: NodeConfig[];
  component: React.ComponentType<NodeProps>;
  execute?: (inputs: any, config: any) => Promise<any>;
}

export interface NodeInput {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'file';
  required: boolean;
  description?: string;
  defaultValue?: any;
}

export interface NodeOutput {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'file';
  description?: string;
}

export interface NodeConfig {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'textarea' | 'password';
  required: boolean;
  description?: string;
  defaultValue?: any;
  options?: { label: string; value: any }[];
}

export class NodeLoader {
  private static instance: NodeLoader;
  private loadedNodes: Map<string, NodeDefinition> = new Map();
  private nodeCache: Map<string, string> = new Map();

  static getInstance(): NodeLoader {
    if (!NodeLoader.instance) {
      NodeLoader.instance = new NodeLoader();
    }
    return NodeLoader.instance;
  }

  async loadNode(nodeId: string, version?: string): Promise<NodeDefinition | null> {
    try {
      // Check if node is already loaded
      const cacheKey = `${nodeId}:${version || 'latest'}`;
      if (this.loadedNodes.has(cacheKey)) {
        return this.loadedNodes.get(cacheKey)!;
      }

      // Fetch node code from API
      const response = await fetch(`/api/nodes/code/${nodeId}${version ? `?version=${version}` : ''}`);
      if (!response.ok) {
        throw new Error(`Failed to load node: ${response.statusText}`);
      }

      const { nodeCode } = await response.json();

      // Create a secure execution environment
      const nodeDefinition = await this.executeNodeCode(nodeCode);

      // Cache the loaded node
      this.loadedNodes.set(cacheKey, nodeDefinition);

      return nodeDefinition;
    } catch (error) {
      console.error(`Failed to load node ${nodeId}:`, error);
      return null;
    }
  }

  private async executeNodeCode(nodeCode: any): Promise<NodeDefinition> {
    return new Promise((resolve, reject) => {
      try {
        // Create a Web Worker for secure execution
        const workerCode = `
          try {
            // Execute the node code
            ${nodeCode.code}

            // The node code should define a nodeDefinition variable
            // Send it back to the main thread
            if (typeof nodeDefinition !== 'undefined') {
              self.postMessage({ type: 'definition', data: nodeDefinition });
            } else {
              self.postMessage({ type: 'error', error: 'nodeDefinition not found in node code' });
            }
          } catch (error) {
            self.postMessage({
              type: 'error',
              error: 'Failed to execute node code: ' + (error.message || error.toString())
            });
          }
        `;

        const blob = new Blob([workerCode], { type: 'application/javascript' });
        const blobUrl = URL.createObjectURL(blob);
        const worker = new Worker(blobUrl);

        const timeout = setTimeout(() => {
          worker.terminate();
          URL.revokeObjectURL(blobUrl);
          reject(new Error('Node loading timeout (5s)'));
        }, 5000);

        worker.onmessage = (event) => {
          clearTimeout(timeout);
          worker.terminate();
          URL.revokeObjectURL(blobUrl);

          if (event.data.type === 'definition') {
            // Validate the node definition structure
            const definition = event.data.data;
            if (!definition.id || !definition.name || !definition.execute) {
              reject(new Error('Invalid node definition: missing required fields (id, name, execute)'));
              return;
            }
            resolve(definition);
          } else {
            reject(new Error(event.data.error || 'Unknown error loading node'));
          }
        };

        worker.onerror = (error) => {
          clearTimeout(timeout);
          worker.terminate();
          URL.revokeObjectURL(blobUrl);
          reject(new Error(`Worker error: ${error.message || 'Unknown worker error'}`));
        };

      } catch (error) {
        reject(new Error(`Failed to create worker: ${error instanceof Error ? error.message : 'Unknown error'}`));
      }
    });
  }

  async getInstalledNodes(): Promise<NodeDefinition[]> {
    try {
      const response = await fetch('/api/nodes/installed');
      if (!response.ok) {
        throw new Error('Failed to fetch installed nodes');
      }

      const { installedNodes } = await response.json();
      const nodeDefinitions: NodeDefinition[] = [];

      for (const installation of installedNodes) {
        if (installation.enabled && installation.status === 'installed') {
          const nodeDefinition = await this.loadNode(installation.nodeId, installation.version);
          if (nodeDefinition) {
            nodeDefinitions.push(nodeDefinition);
          }
        }
      }

      return nodeDefinitions;
    } catch (error) {
      console.error('Failed to get installed nodes:', error);
      return [];
    }
  }

  async installNode(nodeId: string, version?: string): Promise<boolean> {
    try {
      const response = await fetch('/api/nodes/install', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodeId, version }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Installation failed');
      }

      // Clear cache to force reload
      const cacheKey = `${nodeId}:${version || 'latest'}`;
      this.loadedNodes.delete(cacheKey);

      return true;
    } catch (error) {
      console.error(`Failed to install node ${nodeId}:`, error);
      return false;
    }
  }

  async uninstallNode(nodeId: string): Promise<boolean> {
    try {
      const response = await fetch('/api/nodes/uninstall', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodeId }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Uninstallation failed');
      }

      // Remove from cache
      for (const [key] of this.loadedNodes) {
        if (key.startsWith(`${nodeId}:`)) {
          this.loadedNodes.delete(key);
        }
      }

      return true;
    } catch (error) {
      console.error(`Failed to uninstall node ${nodeId}:`, error);
      return false;
    }
  }

  getLoadedNode(nodeId: string, version?: string): NodeDefinition | null {
    const cacheKey = `${nodeId}:${version || 'latest'}`;
    return this.loadedNodes.get(cacheKey) || null;
  }

  clearCache(): void {
    this.loadedNodes.clear();
    this.nodeCache.clear();
  }
}

// Export singleton instance
export const nodeLoader = NodeLoader.getInstance();
