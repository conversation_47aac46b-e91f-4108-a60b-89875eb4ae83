import { AppSettings } from './types';

export const defaultSettings: AppSettings = {
  // Payment Gateway Settings
  payments: {
    enabled: false,
    provider: 'disabled',
    dokuEnabled: false,
    testMode: true,
    currency: 'idr',
    commissionRate: 0.25,
    minimumAmount: 10000, // IDR 10,000
    maximumAmount: 100000000, // IDR 100,000,000
    // Doku specific settings
    dokuClientId: '',
    dokuSecretKey: '',
    dokuEnvironment: 'sandbox',
    dokuNotificationUrl: '',
  },

  // Billing Settings
  billing: {
    enabled: false,
    invoiceGeneration: false,
    automaticBilling: false,
    billingCycle: 'monthly',
    gracePeriodDays: 7,
    reminderDays: [7, 3, 1],
  },

  // Subscription Settings
  subscriptions: {
    enabled: false,
    allowFreePlan: true,
    allowUpgrades: false,
    allowDowngrades: false,
    prorationEnabled: false,
    trialPeriodDays: 14,
    cancelationPolicy: 'end_of_period',
  },

  // Marketplace Settings
  marketplace: {
    enabled: true,
    paidNodesEnabled: false,
    freeNodesEnabled: true,
    nodeApprovalRequired: true,
    allowNodeUploads: true,
    maxNodeSize: 10, // 10MB
    allowedFileTypes: ['.js', '.ts', '.json'],
    featuredNodesEnabled: true,
    reviewSystemEnabled: true,
    ratingSystemEnabled: true,
  },

  // Developer Settings
  developer: {
    enabled: true,
    nodePublishingEnabled: true,
    analyticsEnabled: true,
    revenueShareEnabled: false,
    maxNodesPerDeveloper: 50,
    approvalProcessEnabled: true,
    sandboxTestingEnabled: true,
  },

  // Workflow Settings
  workflows: {
    enabled: true,
    maxWorkflowsPerUser: 100,
    maxNodesPerWorkflow: 50,
    executionTimeoutMinutes: 30,
    schedulingEnabled: true,
    sharingEnabled: true,
    exportEnabled: true,
    importEnabled: true,
  },

  // Email Settings
  email: {
    enabled: false,
    provider: 'disabled',
    verificationEnabled: false,
    notificationsEnabled: false,
    marketingEmailsEnabled: false,
    fromEmail: '<EMAIL>',
    fromName: 'Workflow App',
  },

  // Security Settings
  security: {
    twoFactorEnabled: false,
    passwordRequirements: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: false,
    },
    sessionTimeoutMinutes: 480, // 8 hours
    maxLoginAttempts: 5,
    lockoutDurationMinutes: 15,
  },

  // API Settings
  api: {
    rateLimitEnabled: true,
    requestsPerMinute: 100,
    requestsPerHour: 1000,
    apiKeysEnabled: false,
    webhooksEnabled: false,
    corsEnabled: true,
    allowedOrigins: ['http://localhost:3000'],
  },

  // Storage Settings
  storage: {
    provider: 'local',
    maxFileSize: 50, // 50MB
    allowedFileTypes: ['.js', '.ts', '.json', '.md', '.txt'],
    compressionEnabled: true,
    backupEnabled: false,
    retentionDays: 90,
  },

  // Analytics Settings
  analytics: {
    enabled: true,
    trackingEnabled: true,
    dataRetentionDays: 365,
    anonymizeData: true,
    exportEnabled: true,
    realtimeEnabled: true,
  },

  // Maintenance Settings
  maintenance: {
    maintenanceMode: false,
    maintenanceMessage: 'We are currently performing scheduled maintenance. Please check back soon.',
    allowedIPs: [],
    scheduledMaintenanceEnabled: false,
    backupScheduleEnabled: false,
  },

  // Feature Flags
  features: {
    betaFeaturesEnabled: false,
    experimentalFeaturesEnabled: false,
    debugModeEnabled: false,
    performanceMonitoringEnabled: true,
    errorReportingEnabled: true,
  },

  // UI/UX Settings
  ui: {
    darkModeEnabled: true,
    compactModeEnabled: false,
    animationsEnabled: true,
    notificationsEnabled: true,
    soundEnabled: false,
    language: 'en',
    timezone: 'UTC',
  },
};

// Environment-based overrides
export function getEnvironmentSettings(): Partial<AppSettings> {
  const env = process.env.NODE_ENV;

  if (env === 'development') {
    return {
      features: {
        betaFeaturesEnabled: true,
        experimentalFeaturesEnabled: true,
        debugModeEnabled: true,
        performanceMonitoringEnabled: true,
        errorReportingEnabled: true,
      },
      maintenance: {
        maintenanceMode: false,
        maintenanceMessage: 'Development mode - some features may be unstable',
        allowedIPs: ['127.0.0.1', 'localhost'],
        scheduledMaintenanceEnabled: false,
        backupScheduleEnabled: false,
      },
    };
  }

  if (env === 'production') {
    return {
      features: {
        betaFeaturesEnabled: false,
        experimentalFeaturesEnabled: false,
        debugModeEnabled: false,
        performanceMonitoringEnabled: true,
        errorReportingEnabled: true,
      },
      security: {
        twoFactorEnabled: true,
        passwordRequirements: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: true,
        },
        sessionTimeoutMinutes: 240, // 4 hours in production
        maxLoginAttempts: 3,
        lockoutDurationMinutes: 30,
      },
    };
  }

  return {};
}

// Merge default settings with environment overrides
export function getInitialSettings(): AppSettings {
  const envSettings = getEnvironmentSettings();
  return {
    ...defaultSettings,
    ...envSettings,
    // Deep merge for nested objects
    features: { ...defaultSettings.features, ...envSettings.features },
    security: { ...defaultSettings.security, ...envSettings.security },
    maintenance: { ...defaultSettings.maintenance, ...envSettings.maintenance },
  };
}
