/**
 * Performance optimization utilities for database queries
 */

export interface QueryOptimizationConfig {
  maxLimit: number;
  defaultLimit: number;
  cacheTimeout: number;
  enablePagination: boolean;
}

export const DEFAULT_QUERY_CONFIG: QueryOptimizationConfig = {
  maxLimit: 100,
  defaultLimit: 25,
  cacheTimeout: 5 * 60 * 1000, // 5 minutes
  enablePagination: true,
};

/**
 * Optimize pagination parameters
 */
export function optimizePagination(
  page: number = 1,
  limit: number = DEFAULT_QUERY_CONFIG.defaultLimit,
  config: QueryOptimizationConfig = DEFAULT_QUERY_CONFIG
) {
  const optimizedPage = Math.max(1, page);
  const optimizedLimit = Math.min(Math.max(1, limit), config.maxLimit);
  const skip = (optimizedPage - 1) * optimizedLimit;

  return {
    page: optimizedPage,
    limit: optimizedLimit,
    skip,
    take: optimizedLimit,
  };
}

/**
 * Create optimized where clause for search
 */
export function createSearchWhere(
  searchTerm: string,
  searchFields: string[],
  additionalFilters: Record<string, any> = {}
) {
  const where: any = { ...additionalFilters };

  if (searchTerm && searchFields.length > 0) {
    where.OR = searchFields.map(field => ({
      [field]: { contains: searchTerm }
    }));
  }

  return where;
}

/**
 * Performance monitoring for queries
 */
export class QueryPerformanceMonitor {
  private static instance: QueryPerformanceMonitor;
  private queryTimes: Map<string, number[]> = new Map();

  static getInstance(): QueryPerformanceMonitor {
    if (!QueryPerformanceMonitor.instance) {
      QueryPerformanceMonitor.instance = new QueryPerformanceMonitor();
    }
    return QueryPerformanceMonitor.instance;
  }

  startTimer(queryName: string): () => void {
    const startTime = Date.now();
    
    return () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (!this.queryTimes.has(queryName)) {
        this.queryTimes.set(queryName, []);
      }
      
      const times = this.queryTimes.get(queryName)!;
      times.push(duration);
      
      // Keep only last 100 measurements
      if (times.length > 100) {
        times.shift();
      }
      
      // Log slow queries (> 1 second)
      if (duration > 1000) {
        console.warn(`Slow query detected: ${queryName} took ${duration}ms`);
      }
    };
  }

  getAverageTime(queryName: string): number {
    const times = this.queryTimes.get(queryName);
    if (!times || times.length === 0) return 0;
    
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  getSlowQueries(threshold: number = 500): Array<{ name: string; avgTime: number }> {
    const slowQueries: Array<{ name: string; avgTime: number }> = [];
    
    for (const [name, times] of this.queryTimes.entries()) {
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      if (avgTime > threshold) {
        slowQueries.push({ name, avgTime });
      }
    }
    
    return slowQueries.sort((a, b) => b.avgTime - a.avgTime);
  }

  reset(): void {
    this.queryTimes.clear();
  }
}

/**
 * Database cleanup utilities
 */
export class DatabaseCleanup {
  /**
   * Clean old logs (older than specified days)
   */
  static async cleanOldLogs(daysToKeep: number = 90): Promise<number> {
    const { prisma } = await import('@/lib/prisma');
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    try {
      const result = await prisma.userLog.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate,
          },
        },
      });

      console.log(`Cleaned ${result.count} old log entries`);
      return result.count;
    } catch (error) {
      console.error('Error cleaning old logs:', error);
      throw error;
    }
  }

  /**
   * Clean old login history (older than specified days)
   */
  static async cleanOldLoginHistory(daysToKeep: number = 180): Promise<number> {
    const { prisma } = await import('@/lib/prisma');
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    try {
      const result = await prisma.loginHistory.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate,
          },
        },
      });

      console.log(`Cleaned ${result.count} old login history entries`);
      return result.count;
    } catch (error) {
      console.error('Error cleaning old login history:', error);
      throw error;
    }
  }

  /**
   * Get database statistics
   */
  static async getDatabaseStats(): Promise<{
    userCount: number;
    logCount: number;
    loginHistoryCount: number;
    roleCount: number;
    workflowCount: number;
  }> {
    const { prisma } = await import('@/lib/prisma');

    try {
      const [userCount, logCount, loginHistoryCount, roleCount, workflowCount] = await Promise.all([
        prisma.user.count(),
        prisma.userLog.count(),
        prisma.loginHistory.count(),
        prisma.role.count(),
        prisma.workflow.count(),
      ]);

      return {
        userCount,
        logCount,
        loginHistoryCount,
        roleCount,
        workflowCount,
      };
    } catch (error) {
      console.error('Error getting database stats:', error);
      throw error;
    }
  }
}

/**
 * Performance recommendations
 */
export function getPerformanceRecommendations(): string[] {
  const recommendations = [
    "Consider adding database indexes on frequently queried columns",
    "Implement query result caching for static data",
    "Use pagination for large datasets",
    "Regularly clean up old log entries",
    "Monitor slow queries and optimize them",
    "Consider using database connection pooling",
    "Implement proper error handling and retry logic",
    "Use database transactions for related operations",
    "Consider implementing read replicas for heavy read workloads",
    "Monitor database performance metrics regularly"
  ];

  return recommendations;
}
