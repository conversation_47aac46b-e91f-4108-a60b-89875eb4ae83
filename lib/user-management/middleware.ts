// User Management System - Middleware

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';
import { hasPermission, canAccessPage, UserWithRoles } from './permissions';

// Activity logging middleware
export async function logActivity(
  userId: string,
  action: string,
  resource?: string,
  resourceId?: string,
  details?: Record<string, any>,
  request?: NextRequest
) {
  try {
    const ipAddress = request?.ip || 
      request?.headers.get('x-forwarded-for')?.split(',')[0] ||
      request?.headers.get('x-real-ip') ||
      'unknown';
    
    const userAgent = request?.headers.get('user-agent') || 'unknown';

    await prisma.userLog.create({
      data: {
        userId,
        action,
        resource,
        resourceId,
        details: details ? JSON.stringify(details) : null,
        ipAddress,
        userAgent,
      },
    });
  } catch (error) {
    console.error('Failed to log activity:', error);
    // Don't throw error to avoid breaking the main flow
  }
}

// Login history logging
export async function logLoginAttempt(
  userId: string,
  success: boolean,
  failureReason?: string,
  request?: NextRequest
) {
  try {
    const ipAddress = request?.ip || 
      request?.headers.get('x-forwarded-for')?.split(',')[0] ||
      request?.headers.get('x-real-ip') ||
      'unknown';
    
    const userAgent = request?.headers.get('user-agent') || 'unknown';

    await prisma.loginHistory.create({
      data: {
        userId,
        success,
        failureReason,
        ipAddress,
        userAgent,
        // TODO: Add geolocation lookup if needed
        location: null,
      },
    });

    // Update user's last login and login count on successful login
    if (success) {
      await prisma.user.update({
        where: { id: userId },
        data: {
          lastLoginAt: new Date(),
          lastActiveAt: new Date(),
          loginCount: {
            increment: 1,
          },
        },
      });
    }
  } catch (error) {
    console.error('Failed to log login attempt:', error);
  }
}

// Get user with roles and permissions
export async function getUserWithRoles(userId: string): Promise<UserWithRoles | null> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!user) return null;

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      isAdmin: user.isAdmin,
      isSuperAdmin: user.isSuperAdmin,
      userRoles: user.userRoles.map(ur => ({
        role: {
          id: ur.role.id,
          name: ur.role.name,
          displayName: ur.role.displayName,
          description: ur.role.description,
          permissions: ur.role.permissions.map(rp => rp.permission),
        },
      })),
    };
  } catch (error) {
    console.error('Failed to get user with roles:', error);
    return null;
  }
}

// Permission checking middleware for API routes
export function requirePermission(resource: string, action: string) {
  return async function middleware(
    request: NextRequest,
    handler: (req: NextRequest, user: UserWithRoles) => Promise<NextResponse>
  ): Promise<NextResponse> {
    try {
      const session = await getServerSession(authOptions);
      
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const user = await getUserWithRoles(session.user.id);
      
      if (!user) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }

      if (!hasPermission(user, resource, action)) {
        await logActivity(
          user.id,
          'access_denied',
          resource,
          undefined,
          { action, reason: 'insufficient_permissions' },
          request
        );

        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        );
      }

      // Log the API access
      await logActivity(
        user.id,
        'api_called',
        resource,
        undefined,
        { action, endpoint: request.url },
        request
      );

      return handler(request, user);
    } catch (error) {
      console.error('Permission middleware error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

// Admin-only middleware
export function requireAdmin() {
  return requirePermission('admin', 'access');
}

// Super admin-only middleware
export function requireSuperAdmin() {
  return async function middleware(
    request: NextRequest,
    handler: (req: NextRequest, user: UserWithRoles) => Promise<NextResponse>
  ): Promise<NextResponse> {
    try {
      const session = await getServerSession(authOptions);
      
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const user = await getUserWithRoles(session.user.id);
      
      if (!user || !user.isSuperAdmin) {
        await logActivity(
          user?.id || session.user.id,
          'access_denied',
          'admin',
          undefined,
          { reason: 'not_super_admin' },
          request
        );

        return NextResponse.json(
          { error: 'Super admin access required' },
          { status: 403 }
        );
      }

      return handler(request, user);
    } catch (error) {
      console.error('Super admin middleware error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

// Update user's last active timestamp
export async function updateLastActive(userId: string) {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        lastActiveAt: new Date(),
      },
    });
  } catch (error) {
    console.error('Failed to update last active:', error);
  }
}

// Rate limiting helper (basic implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function rateLimit(
  identifier: string,
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): boolean {
  const now = Date.now();
  const windowStart = now - windowMs;

  // Clean up old entries
  for (const [key, value] of rateLimitMap.entries()) {
    if (value.resetTime < now) {
      rateLimitMap.delete(key);
    }
  }

  const current = rateLimitMap.get(identifier);
  
  if (!current) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (current.resetTime < now) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (current.count >= maxRequests) {
    return false;
  }

  current.count++;
  return true;
}

// Validate request data helper
export function validateRequestData<T>(
  data: any,
  requiredFields: (keyof T)[],
  optionalFields: (keyof T)[] = []
): { isValid: boolean; errors: string[]; validatedData?: Partial<T> } {
  const errors: string[] = [];
  const validatedData: Partial<T> = {};

  // Check required fields
  for (const field of requiredFields) {
    if (data[field] === undefined || data[field] === null || data[field] === '') {
      errors.push(`${String(field)} is required`);
    } else {
      validatedData[field] = data[field];
    }
  }

  // Include optional fields if present
  for (const field of optionalFields) {
    if (data[field] !== undefined && data[field] !== null) {
      validatedData[field] = data[field];
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    validatedData: errors.length === 0 ? validatedData : undefined,
  };
}
