import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth-config";
import { prisma } from "@/lib/prisma";
import { unlink, rmdir } from "fs/promises";
import { join } from "path";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ nodeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Await params
    const { nodeId } = await params;

    // Get node (ensure it belongs to the user)
    const node = await prisma.nodePlugin.findFirst({
      where: {
        id: nodeId,
        authorId: user.id
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true
          }
        },
        reviews: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                avatar: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        _count: {
          select: {
            reviews: true,
            purchases: true
          }
        }
      }
    });

    if (!node) {
      return NextResponse.json({ error: 'Node not found' }, { status: 404 });
    }

    // Transform node data
    const transformedNode = {
      ...node,
      status: node.verified ? 'approved' : 'pending',
      reviewCount: node._count.reviews,
      tags: JSON.parse(node.tags || '[]'),
      dependencies: JSON.parse(node.dependencies || '[]'),
      permissions: JSON.parse(node.permissions || '[]'),
      screenshots: JSON.parse(node.screenshots || '[]'),
      compatibility: JSON.parse(node.compatibility || '{}'),
      changelog: JSON.parse(node.changelog || '[]')
    };

    return NextResponse.json(transformedNode);

  } catch (error) {
    console.error('Node fetch error:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ nodeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Await params
    const { nodeId } = await params;

    // Verify node ownership
    const existingNode = await prisma.nodePlugin.findFirst({
      where: {
        id: nodeId,
        authorId: user.id
      }
    });

    if (!existingNode) {
      return NextResponse.json({ error: 'Node not found' }, { status: 404 });
    }

    const formData = await request.formData();

    // Extract form fields
    const name = formData.get('name') as string;
    const version = formData.get('version') as string;
    const description = formData.get('description') as string;
    const longDescription = formData.get('longDescription') as string || '';
    const category = formData.get('category') as string;
    const tier = formData.get('tier') as string;
    const price = parseFloat(formData.get('price') as string || '0');
    const tags = JSON.parse(formData.get('tags') as string || '[]');
    const repositoryUrl = formData.get('repositoryUrl') as string || '';
    const documentationUrl = formData.get('documentationUrl') as string || '';

    // Validate required fields
    if (!name || !version || !description || !category || !tier) {
      return NextResponse.json({
        error: 'Missing required fields'
      }, { status: 400 });
    }

    // Update node in database
    const updatedNode = await prisma.nodePlugin.update({
      where: { id: nodeId },
      data: {
        name,
        version,
        description,
        longDescription,
        category,
        tier,
        price: tier === 'free' ? 0 : price,
        tags: JSON.stringify(tags),
        repositoryUrl: repositoryUrl || null,
        documentationUrl: documentationUrl || null,
        verified: false, // Reset verification status on update
        lastUpdated: new Date()
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true
          }
        }
      }
    });

    // Transform response
    const transformedNode = {
      ...updatedNode,
      status: 'pending',
      tags: JSON.parse(updatedNode.tags || '[]'),
      dependencies: JSON.parse(updatedNode.dependencies || '[]'),
      permissions: JSON.parse(updatedNode.permissions || '[]'),
      screenshots: JSON.parse(updatedNode.screenshots || '[]'),
      compatibility: JSON.parse(updatedNode.compatibility || '{}')
    };

    return NextResponse.json(transformedNode);

  } catch (error) {
    console.error('Node update error:', error);
    return NextResponse.json({
      error: 'Failed to update node'
    }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ nodeId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Await params
    const { nodeId } = await params;

    // Verify node ownership
    const node = await prisma.nodePlugin.findFirst({
      where: {
        id: nodeId,
        authorId: user.id
      }
    });

    if (!node) {
      return NextResponse.json({ error: 'Node not found' }, { status: 404 });
    }

    // Check if node has active installations
    const installations = await prisma.installedNode.count({
      where: { nodeId: nodeId }
    });

    if (installations > 0) {
      return NextResponse.json({
        error: 'Cannot delete node with active installations'
      }, { status: 400 });
    }

    // Delete related records first
    await prisma.nodeReview.deleteMany({
      where: { nodeId: nodeId }
    });

    await prisma.nodePurchase.deleteMany({
      where: { nodeId: nodeId }
    });

    await prisma.nodeCode.deleteMany({
      where: { nodeId: nodeId }
    });

    await prisma.userLibrary.deleteMany({
      where: { nodeId: nodeId }
    });

    // Delete the node
    await prisma.nodePlugin.delete({
      where: { id: nodeId }
    });

    // Clean up files
    try {
      const nodeDir = join(process.cwd(), 'uploads', 'nodes', user.id, nodeId);
      await rmdir(nodeDir, { recursive: true });
    } catch (fileError) {
      console.warn('Failed to delete node files:', fileError);
      // Continue even if file deletion fails
    }

    return NextResponse.json({
      success: true,
      message: 'Node deleted successfully'
    });

  } catch (error) {
    console.error('Node deletion error:', error);
    return NextResponse.json({
      error: 'Failed to delete node'
    }, { status: 500 });
  }
}
