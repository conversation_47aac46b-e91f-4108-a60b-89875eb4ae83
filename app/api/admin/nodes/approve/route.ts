import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has node approval permissions
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        isAdmin: true,
        isSuperAdmin: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has admin permissions (simplified check for now)
    if (!user.isAdmin && !user.isSuperAdmin) {
      return NextResponse.json(
        { error: 'Admin access required to approve nodes' },
        { status: 403 }
      );
    }

    const { nodeId, action, rejectionReason } = await request.json();

    if (!nodeId || !action) {
      return NextResponse.json(
        { error: 'Node ID and action are required' },
        { status: 400 }
      );
    }

    if (!['approve', 'reject', 'pending'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be either "approve", "reject", or "pending"' },
        { status: 400 }
      );
    }

    if (action === 'reject' && !rejectionReason) {
      return NextResponse.json(
        { error: 'Rejection reason is required when rejecting a node' },
        { status: 400 }
      );
    }

    // Find the node
    const node = await prisma.nodePlugin.findUnique({
      where: { id: nodeId },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!node) {
      return NextResponse.json(
        { error: 'Node not found' },
        { status: 404 }
      );
    }

    // Allow status changes from any status to any other status
    // Remove the restriction that only allows changes from pending status

    // Update node approval status
    const updatedNode = await prisma.nodePlugin.update({
      where: { id: nodeId },
      data: {
        approvalStatus: action,
        approvedAt: action === 'approve' ? new Date() : null,
        approvedById: action === 'approve' ? session.user.id : null,
        rejectionReason: action === 'reject' ? rejectionReason : null
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        approvedBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Log the approval action
    await prisma.userLog.create({
      data: {
        userId: session.user.id,
        action: action === 'approve' ? 'approve_node' : action === 'reject' ? 'reject_node' : 'reset_node_to_pending',
        resource: 'marketplace',
        resourceId: nodeId,
        details: JSON.stringify({
          nodeName: node.name,
          nodeVersion: node.version,
          authorId: node.authorId,
          authorName: node.author.name,
          rejectionReason: action === 'reject' ? rejectionReason : undefined,
          previousStatus: node.approvalStatus
        })
      }
    });

    // TODO: Send notification email to node author
    // This would be implemented with your email service
    console.log(`Node ${action}d:`, {
      nodeId,
      nodeName: node.name,
      authorEmail: node.author.email,
      action,
      rejectionReason: action === 'reject' ? rejectionReason : undefined
    });

    return NextResponse.json({
      success: true,
      message: `Node status changed to ${action} successfully`,
      node: {
        id: updatedNode.id,
        name: updatedNode.name,
        version: updatedNode.version,
        approvalStatus: updatedNode.approvalStatus,
        approvedAt: updatedNode.approvedAt,
        approvedBy: updatedNode.approvedBy,
        rejectionReason: updatedNode.rejectionReason,
        author: updatedNode.author
      }
    });

  } catch (error) {
    console.error('Node approval error:', error);
    return NextResponse.json(
      { error: 'Failed to process node approval' },
      { status: 500 }
    );
  }
}
