"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Package, Calendar, Download, Star, Clock, CheckCircle, XCircle, AlertCircle, Trash2, Edit } from "lucide-react";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface DeveloperNode {
  id: string;
  name: string;
  version: string;
  description: string;
  category: string;
  tier: string;
  price: number;
  icon: string;
  tags: string[];
  approvalStatus: string;
  approvedAt: string | null;
  rejectionReason: string | null;
  createdAt: string;
  lastUpdated: string;
  approvedBy?: {
    id: string;
    name: string;
    email: string;
  };
  hasCode: boolean;
  installCount: number;
  reviewCount: number;
  purchaseCount: number;
  rating: number;
  downloads: number;
  weeklyDownloads: number;
  verified: boolean;
  featured: boolean;
}

interface Stats {
  pending: number;
  approved: number;
  rejected: number;
  total: number;
  totalDownloads: number;
  totalWeeklyDownloads: number;
  totalPurchases: number;
}

export default function DeveloperNodeStatusPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [nodes, setNodes] = useState<DeveloperNode[]>([]);
  const [stats, setStats] = useState<Stats>({
    pending: 0,
    approved: 0,
    rejected: 0,
    total: 0,
    totalDownloads: 0,
    totalWeeklyDownloads: 0,
    totalPurchases: 0
  });
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  const [deletingNodeId, setDeletingNodeId] = useState<string | null>(null);

  // Check admin access
  useEffect(() => {
    if (status === "loading") return;

    if (!session?.user) {
      router.push("/auth/signin");
      return;
    }

    const user = session.user as any;
    if (!user.isAdmin && !user.isSuperAdmin) {
      router.push("/dashboard");
      return;
    }
  }, [session, status, router]);

  // Load nodes data
  const loadNodes = async (statusFilter: string = activeTab) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/developer/nodes/status?status=${statusFilter}&limit=50`, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch nodes');
      }

      const data = await response.json();
      setNodes(data.data.nodes);
      setStats(data.data.stats);
    } catch (error) {
      console.error('Error loading nodes:', error);
      toast.error('Failed to load node status');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (session?.user) {
      loadNodes(activeTab);
    }
  }, [session, activeTab]);

  // Delete node function
  const deleteNode = async (nodeId: string, nodeName: string) => {
    try {
      setDeletingNodeId(nodeId);

      const response = await fetch(`/api/developer/nodes/${nodeId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete node');
      }

      // Remove node from local state
      setNodes(prevNodes => prevNodes.filter(node => node.id !== nodeId));

      // Update stats
      setStats(prevStats => {
        const deletedNode = nodes.find(node => node.id === nodeId);
        if (!deletedNode) return prevStats;

        const newStats = { ...prevStats };
        newStats.total = Math.max(0, newStats.total - 1);

        // Decrease count for the deleted node's status
        if (deletedNode.approvalStatus === 'pending') {
          newStats.pending = Math.max(0, newStats.pending - 1);
        } else if (deletedNode.approvalStatus === 'approved') {
          newStats.approved = Math.max(0, newStats.approved - 1);
          newStats.totalDownloads = Math.max(0, newStats.totalDownloads - deletedNode.downloads);
          newStats.totalWeeklyDownloads = Math.max(0, newStats.totalWeeklyDownloads - deletedNode.weeklyDownloads);
          newStats.totalPurchases = Math.max(0, newStats.totalPurchases - deletedNode.purchaseCount);
        } else if (deletedNode.approvalStatus === 'rejected') {
          newStats.rejected = Math.max(0, newStats.rejected - 1);
        }

        return newStats;
      });

      toast.success(`Node "${nodeName}" deleted successfully`);
    } catch (error) {
      console.error('Error deleting node:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete node');
    } finally {
      setDeletingNodeId(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="outline" className="text-yellow-600 border-yellow-600">
            <Clock className="h-3 w-3 mr-1" />
            Pending Review
          </Badge>
        );
      case 'approved':
        return (
          <Badge variant="outline" className="text-green-600 border-green-600">
            <CheckCircle className="h-3 w-3 mr-1" />
            Approved
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="text-red-600 border-red-600">
            <XCircle className="h-3 w-3 mr-1" />
            Rejected
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-muted-foreground" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!session?.user) {
    return null;
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/developer">Developer</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Node Status</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">My Node Status</h1>
          <p className="text-muted-foreground">Track the approval status of your submitted nodes</p>
        </div>
        <Button onClick={() => router.push('/developer/upload')}>
          Upload New Node
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">Awaiting admin approval</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
            <p className="text-xs text-muted-foreground">Live in marketplace</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Downloads</CardTitle>
            <Download className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.totalDownloads}</div>
            <p className="text-xs text-muted-foreground">{stats.totalWeeklyDownloads} this week</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Nodes</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">{stats.rejected} rejected</p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different statuses */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All Nodes ({stats.total})</TabsTrigger>
          <TabsTrigger value="pending">Pending ({stats.pending})</TabsTrigger>
          <TabsTrigger value="approved">Approved ({stats.approved})</TabsTrigger>
          <TabsTrigger value="rejected">Rejected ({stats.rejected})</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : nodes.length === 0 ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <div className="text-center">
                  <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No nodes found</p>
                  <Button
                    className="mt-4"
                    onClick={() => router.push('/developer/upload')}
                  >
                    Upload Your First Node
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {nodes.map((node) => (
                <Card key={node.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <CardTitle className="flex items-center gap-2">
                          {getStatusIcon(node.approvalStatus)}
                          {node.name}
                          <Badge variant="outline">{node.version}</Badge>
                          {getStatusBadge(node.approvalStatus)}
                          {node.featured && <Badge className="bg-purple-100 text-purple-800">Featured</Badge>}
                          {node.verified && <Badge className="bg-blue-100 text-blue-800">Verified</Badge>}
                        </CardTitle>
                        <CardDescription>{node.description}</CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => toast.info("Edit functionality coming soon. For now, you can upload a new version.")}
                          disabled={deletingNodeId === node.id}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              disabled={deletingNodeId === node.id}
                              className="text-red-600 border-red-600 hover:bg-red-50"
                            >
                              {deletingNodeId === node.id ? (
                                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                              ) : (
                                <Trash2 className="h-4 w-4 mr-1" />
                              )}
                              Delete
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Node</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete "{node.name}"? This action cannot be undone.
                                <br /><br />
                                <strong>The following will be permanently removed:</strong>
                                <ul className="list-disc list-inside mt-2 space-y-1">
                                  <li>Node package files and uploads</li>
                                  <li>All reviews and ratings</li>
                                  <li>Purchase and installation history</li>
                                  <li>Node code and dependencies</li>
                                  <li>Screenshots and documentation</li>
                                </ul>
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => deleteNode(node.id, node.name)}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                Delete Node
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>Submitted {formatDate(node.createdAt)}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Download className="h-4 w-4 text-muted-foreground" />
                        <span>{node.installCount} installs</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-muted-foreground" />
                        <span>{node.rating.toFixed(1)} ({node.reviewCount} reviews)</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4 text-muted-foreground" />
                        <span>{node.tier} • {node.category}</span>
                      </div>
                    </div>

                    {node.approvalStatus === 'pending' && (
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                        <p className="text-sm text-yellow-700">
                          <strong>Status:</strong> Your node is currently under review by our team.
                          This process typically takes 1-3 business days.
                        </p>
                      </div>
                    )}

                    {node.rejectionReason && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded">
                        <p className="text-sm text-red-700">
                          <strong>Rejection Reason:</strong> {node.rejectionReason}
                        </p>
                        <p className="text-xs text-red-600 mt-2">
                          You can update your node and resubmit for review.
                        </p>
                      </div>
                    )}

                    {node.approvedBy && node.approvedAt && (
                      <div className="p-3 bg-green-50 border border-green-200 rounded">
                        <p className="text-sm text-green-700">
                          <strong>Approved:</strong> {formatDate(node.approvedAt)} by {node.approvedBy.name}
                        </p>
                        <p className="text-xs text-green-600 mt-1">
                          Your node is now live in the marketplace!
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
