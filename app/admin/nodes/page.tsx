"use client";

import React from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Check, X, Package, RotateCcw } from "lucide-react";

export default function AdminNodesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [mounted, setMounted] = React.useState(false);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [nodes, setNodes] = React.useState<any[]>([]);
  const [stats, setStats] = React.useState({ pending: 0, approved: 0, rejected: 0, total: 0 });
  const [statusFilter, setStatusFilter] = React.useState('pending');
  const [actionLoading, setActionLoading] = React.useState<string | null>(null);
  const [showRejectDialog, setShowRejectDialog] = React.useState(false);
  const [selectedNode, setSelectedNode] = React.useState<any>(null);
  const [rejectionReason, setRejectionReason] = React.useState('');

  // Check admin access
  React.useEffect(() => {
    if (status === "loading") return;

    if (!session?.user) {
      router.push("/auth/signin");
      return;
    }

    const user = session.user as any;
    if (!user.isAdmin && !user.isSuperAdmin) {
      router.push("/dashboard");
      return;
    }

    setMounted(true);
  }, [session, status, router]);

  // Function to refresh stats from server
  const refreshStats = async () => {
    try {
      const statsResponse = await fetch('/api/admin/nodes/pending?status=all&limit=1', {
        credentials: 'include'
      });

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data?.stats || { pending: 0, approved: 0, rejected: 0, total: 0 });
      }
    } catch (err) {
      console.error('Error refreshing stats:', err);
    }
  };

  React.useEffect(() => {
    if (!mounted) return;

    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load nodes for current filter
        const response = await fetch(`/api/admin/nodes/pending?status=${statusFilter}&limit=20`, {
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`Failed to load nodes: ${response.status}`);
        }

        const data = await response.json();
        setNodes(data.data?.nodes || []);

        // Always get fresh stats from 'all' status for accuracy
        await refreshStats();

      } catch (err) {
        console.error('Error loading nodes:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [mounted, statusFilter]);

  const getStatusBadge = React.useCallback((status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>;
      case 'approved':
        return <Badge variant="outline" className="text-green-600 border-green-600">Approved</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="text-red-600 border-red-600">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  }, []);

  const handleNodeAction = async (nodeId: string, action: string, rejectionReason?: string) => {
    try {
      setActionLoading(nodeId);

      // Find the current node to get its current status for optimistic updates
      const currentNode = nodes.find(node => node.id === nodeId);
      const currentStatus = currentNode?.approvalStatus || 'pending';

      // Optimistically update stats immediately
      if (currentNode) {
        setStats(prevStats => {
          const newStats = { ...prevStats };

          // Decrease count for current status
          if (currentStatus in newStats) {
            newStats[currentStatus as keyof typeof newStats] = Math.max(0, newStats[currentStatus as keyof typeof newStats] - 1);
          }

          // Increase count for new status
          if (action in newStats) {
            newStats[action as keyof typeof newStats] = newStats[action as keyof typeof newStats] + 1;
          }

          return newStats;
        });
      }

      const response = await fetch('/api/admin/nodes/approve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          nodeId,
          action,
          rejectionReason
        })
      });

      if (!response.ok) {
        // Revert optimistic update on error
        if (currentNode) {
          setStats(prevStats => {
            const newStats = { ...prevStats };

            // Revert: increase count for original status
            if (currentStatus in newStats) {
              newStats[currentStatus as keyof typeof newStats] = newStats[currentStatus as keyof typeof newStats] + 1;
            }

            // Revert: decrease count for new status
            if (action in newStats) {
              newStats[action as keyof typeof newStats] = Math.max(0, newStats[action as keyof typeof newStats] - 1);
            }

            return newStats;
          });
        }
        throw new Error(`Failed to ${action} node`);
      }

      // Reload current view data
      const reloadResponse = await fetch(`/api/admin/nodes/pending?status=${statusFilter}&limit=20`, {
        credentials: 'include'
      });

      if (reloadResponse.ok) {
        const data = await reloadResponse.json();
        setNodes(data.data?.nodes || []);
      }

      // Always fetch fresh stats to ensure accuracy
      await refreshStats();

    } catch (err) {
      console.error(`Error ${action}ing node:`, err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setActionLoading(null);
    }
  };

  const handleApprove = (nodeId: string) => {
    handleNodeAction(nodeId, 'approve');
  };

  const handleReject = (node: any) => {
    setSelectedNode(node);
    setShowRejectDialog(true);
  };

  const handleRejectConfirm = () => {
    if (selectedNode && rejectionReason.trim()) {
      handleNodeAction(selectedNode.id, 'reject', rejectionReason);
      setShowRejectDialog(false);
      setSelectedNode(null);
      setRejectionReason('');
    }
  };

  const handleResetToPending = (nodeId: string) => {
    handleNodeAction(nodeId, 'pending');
  };

  const getActionButtons = (node: any) => {
    const isLoading = actionLoading === node.id;

    switch (node.approvalStatus) {
      case 'pending':
        return (
          <div className="flex gap-2">
            <Button
              size="sm"
              className="bg-green-600 hover:bg-green-700"
              onClick={() => handleApprove(node.id)}
              disabled={isLoading}
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-1" /> : <Check className="h-4 w-4 mr-1" />}
              Approve
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="text-red-600 border-red-600"
              onClick={() => handleReject(node)}
              disabled={isLoading}
            >
              <X className="h-4 w-4 mr-1" />
              Reject
            </Button>
          </div>
        );
      case 'approved':
        return (
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleResetToPending(node.id)}
            disabled={isLoading}
          >
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-1" /> : <RotateCcw className="h-4 w-4 mr-1" />}
            Reset to Pending
          </Button>
        );
      case 'rejected':
        return (
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleResetToPending(node.id)}
            disabled={isLoading}
          >
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-1" /> : <RotateCcw className="h-4 w-4 mr-1" />}
            Reset to Pending
          </Button>
        );
      default:
        return null;
    }
  };

  if (status === "loading" || !mounted) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/admin">Administration</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Node Approval</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col items-center justify-center p-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
            <p className="text-muted-foreground">Loading node approval system...</p>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (!session?.user) {
    return null;
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 data-[orientation=vertical]:h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin">Administration</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Node Approval</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>

        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Node Approval System</h1>
          <p className="text-muted-foreground">Review and approve developer-submitted nodes</p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Nodes</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-700">Error: {error}</p>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Package className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <Check className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            <X className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            {statusFilter === 'all' ? 'All Nodes' :
             statusFilter === 'pending' ? 'Pending Nodes' :
             statusFilter === 'approved' ? 'Approved Nodes' : 'Rejected Nodes'}
          </CardTitle>
          <CardDescription>
            {statusFilter === 'all' ? 'All nodes in the system' :
             statusFilter === 'pending' ? 'Nodes waiting for approval' :
             statusFilter === 'approved' ? 'Nodes that have been approved' : 'Nodes that have been rejected'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Loading nodes...</span>
            </div>
          ) : nodes.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {statusFilter === 'all' ? 'No nodes found' :
                 statusFilter === 'pending' ? 'No pending nodes found' :
                 statusFilter === 'approved' ? 'No approved nodes found' : 'No rejected nodes found'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {nodes.map((node: any) => (
                <div key={node.id} className="border rounded p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold">{node.name || 'Unnamed Node'}</h3>
                      <p className="text-sm text-muted-foreground">{node.description || 'No description'}</p>
                      <div className="mt-2 flex items-center gap-4">
                        {getStatusBadge(node.approvalStatus || 'pending')}
                        {node.author && (
                          <span className="text-xs text-muted-foreground">
                            by {node.author.name || node.author.email}
                          </span>
                        )}
                        {node.rejectionReason && (
                          <span className="text-xs text-red-600">
                            Reason: {node.rejectionReason}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {getActionButtons(node)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
        </div>
      </SidebarInset>

      {/* Rejection Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Node</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting "{selectedNode?.name}". This will help the developer understand what needs to be improved.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Enter rejection reason..."
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              rows={4}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRejectDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleRejectConfirm}
              disabled={!rejectionReason.trim()}
              className="bg-red-600 hover:bg-red-700"
            >
              Reject Node
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </SidebarProvider>
  );
}
