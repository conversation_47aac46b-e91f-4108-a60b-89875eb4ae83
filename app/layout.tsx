import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { LoadingProvider } from "@/context/loading-context";
import LoadingWrapper from "@/components/loading-wrapper";
import ConditionalNavbar from "@/components/conditional-navbar";
import NextAuthSessionProvider from "@/components/session-provider";
import { SettingsProvider } from "@/lib/settings/settings-context";

// Optimize font loading
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: 'swap', // Use 'swap' to ensure text remains visible during font loading
  preload: true,
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: 'swap',
  preload: true,
});

export const metadata: Metadata = {
  title: "WorkflowAI - Automate with Intelligence",
  description: "Build powerful automation workflows with our visual editor. Connect your favorite tools and scale your business operations effortlessly.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <NextAuthSessionProvider>
          <SettingsProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
            >
              <LoadingProvider>
                <div className="min-h-screen flex flex-col">
                  <ConditionalNavbar />
                  <main className="flex-1 w-full">
                    {children}
                  </main>
                  <LoadingWrapper />
                </div>
              </LoadingProvider>
            </ThemeProvider>
          </SettingsProvider>
        </NextAuthSessionProvider>
      </body>
    </html>
  );
}
