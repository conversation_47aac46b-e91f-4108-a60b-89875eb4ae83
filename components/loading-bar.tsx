"use client";

import { useEffect, useState } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";
import { useTheme } from "@/components/theme-provider";

export default function LoadingBar() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { isDarkTheme } = useTheme();
  
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  
  // Reset loading state when route changes
  useEffect(() => {
    // Start loading
    setLoading(true);
    setProgress(0);
    
    // Clear any existing timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    // Simulate progress
    const interval = setInterval(() => {
      setProgress((prev) => {
        // Slow down progress as it gets closer to 90%
        if (prev >= 90) {
          return prev;
        }
        if (prev >= 80) {
          return prev + 0.5;
        }
        if (prev >= 70) {
          return prev + 1;
        }
        return prev + 5;
      });
    }, 100);
    
    // Complete loading after a short delay
    const timeout = setTimeout(() => {
      clearInterval(interval);
      setProgress(100);
      
      // Hide the loading bar after it completes
      const hideTimeout = setTimeout(() => {
        setLoading(false);
        setProgress(0);
      }, 300);
      
      setTimeoutId(hideTimeout);
    }, 500);
    
    return () => {
      clearInterval(interval);
      clearTimeout(timeout);
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [pathname, searchParams]);
  
  return (
    <div
      className={cn(
        "fixed top-0 left-0 right-0 z-50 h-1 transition-opacity duration-300",
        loading ? "opacity-100" : "opacity-0"
      )}
    >
      <div
        className={cn(
          "h-full transition-all ease-out duration-300",
          isDarkTheme ? "bg-stone-300" : "bg-stone-700"
        )}
        style={{ width: `${progress}%` }}
      />
    </div>
  );
}
