"use client";

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard, Shield, CheckCircle, ExternalLink } from 'lucide-react';
import { NodePlugin } from '@/lib/marketplace/types';
import { formatPrice } from '@/lib/doku/config';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  node: NodePlugin;
  onSuccess: (nodeId: string) => void;
}

export function PaymentModal({ isOpen, onClose, node, onSuccess }: PaymentModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Purchase Node</DialogTitle>
          <DialogDescription>
            Complete your purchase to install this node
          </DialogDescription>
        </DialogHeader>

        <PaymentForm node={node} onClose={onClose} onSuccess={onSuccess} />
      </DialogContent>
    </Dialog>
  );
}

interface PaymentFormProps {
  node: NodePlugin;
  onClose: () => void;
  onSuccess: (nodeId: string) => void;
}

function PaymentForm({ node, onClose, onSuccess }: PaymentFormProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [checkoutUrl, setCheckoutUrl] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    setIsProcessing(true);
    setError(null);

    try {
      // Create checkout session
      const response = await fetch('/api/payments/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nodeId: node.id,
          amount: node.price,
          currency: 'idr',
          metadata: {
            nodeName: node.name,
            nodeVersion: node.version
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create checkout session');
      }

      const { checkoutUrl: url } = await response.json();

      // Redirect to Doku checkout page
      if (url) {
        window.open(url, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
        setSuccess(true);
        setTimeout(() => {
          onSuccess(node.id);
          onClose();
        }, 2000);
      }

    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsProcessing(false);
    }
  };

  if (success) {
    return (
      <div className="text-center py-6">
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Redirecting to Payment...</h3>
        <p className="text-muted-foreground">
          Complete your payment in the new window to install {node.name}
        </p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Node Details */}
      <div className="flex items-center space-x-3 p-4 bg-muted rounded-lg">
        <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
          <span className="text-2xl">{node.icon}</span>
        </div>
        <div className="flex-1">
          <h4 className="font-semibold">{node.name}</h4>
          <p className="text-sm text-muted-foreground">v{node.version}</p>
        </div>
        <div className="text-right">
          <div className="text-lg font-bold">{formatPrice(node.price, 'IDR')}</div>
          <Badge variant={node.tier === 'premium' ? 'default' : 'secondary'}>
            {node.tier}
          </Badge>
        </div>
      </div>

      {/* Payment Details */}
      <div className="space-y-4">
        <div className="flex justify-between text-sm">
          <span>Node Price</span>
          <span>{formatPrice(node.price, 'IDR')}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Processing Fee</span>
          <span>Included</span>
        </div>
        <Separator />
        <div className="flex justify-between font-semibold">
          <span>Total</span>
          <span>{formatPrice(node.price, 'IDR')}</span>
        </div>
      </div>

      {/* Payment Methods Info */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Available Payment Methods</label>
        <div className="p-3 border rounded-md bg-muted/50">
          <div className="text-sm text-muted-foreground">
            • Virtual Account (BCA, Mandiri, BRI, BNI, etc.)
            <br />
            • E-Wallet (OVO, DANA, ShopeePay, etc.)
            <br />
            • QRIS
            <br />
            • Credit Card
            <br />
            • Convenience Store (Alfamart, Indomaret)
          </div>
        </div>
      </div>

      {/* Security Notice */}
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Shield className="h-4 w-4" />
        <span>Secured by Doku Payment Gateway</span>
      </div>

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onClose}
          disabled={isProcessing}
          className="flex-1"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isProcessing}
          className="flex-1"
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Creating Payment...
            </>
          ) : (
            <>
              <ExternalLink className="h-4 w-4 mr-2" />
              Pay {formatPrice(node.price, 'IDR')}
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
