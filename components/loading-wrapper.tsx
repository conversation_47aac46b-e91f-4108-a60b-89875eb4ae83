"use client";

import { useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import LoadingScreen from "./loading-screen";
import LoadingBar from "./loading-bar";
import { useLoading } from "@/context/loading-context";

export default function LoadingWrapper() {
  const { isLoading, loadingMessage } = useLoading();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // Reset loading state when route changes
  useEffect(() => {
    // This effect is just to ensure the component re-renders on route changes
    // The actual loading bar logic is in the LoadingBar component
  }, [pathname, searchParams]);
  
  return (
    <>
      {/* Top loading bar for page transitions */}
      <LoadingBar />
      
      {/* Full-screen loading overlay for global loading states */}
      <LoadingScreen 
        isLoading={isLoading} 
        message={loadingMessage} 
      />
    </>
  );
}
