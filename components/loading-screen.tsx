"use client";

import { useEffect, useState } from "react";
import { Loader2 } from "lucide-react";
import { useTheme } from "@/components/theme-provider";
import { cn } from "@/lib/utils";

interface LoadingScreenProps {
  /**
   * Optional message to display during loading
   */
  message?: string;
  
  /**
   * Whether to show the loading screen
   */
  isLoading?: boolean;
  
  /**
   * Optional className to apply to the loading screen
   */
  className?: string;
  
  /**
   * Optional delay before showing the loading screen (ms)
   * Prevents flashing for quick loads
   */
  delay?: number;
}

export default function LoadingScreen({
  message = "Loading...",
  isLoading = true,
  className,
  delay = 300,
}: LoadingScreenProps) {
  const { isDarkTheme } = useTheme();
  const [showLoader, setShowLoader] = useState(false);
  
  // Add a slight delay before showing the loader to prevent flashing
  useEffect(() => {
    if (!isLoading) {
      setShowLoader(false);
      return;
    }
    
    const timer = setTimeout(() => {
      if (isLoading) {
        setShowLoader(true);
      }
    }, delay);
    
    return () => clearTimeout(timer);
  }, [isLoading, delay]);
  
  if (!showLoader && !isLoading) {
    return null;
  }
  
  return (
    <div
      className={cn(
        "fixed inset-0 z-50 flex flex-col items-center justify-center bg-background/80 backdrop-blur-sm transition-all duration-300",
        showLoader ? "opacity-100" : "opacity-0 pointer-events-none",
        className
      )}
    >
      <div className="flex flex-col items-center gap-4 text-center">
        <div className="relative">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className={cn(
              "h-2 w-2 rounded-full",
              isDarkTheme ? "bg-stone-300" : "bg-stone-700"
            )} />
          </div>
        </div>
        <p className={cn(
          "text-lg font-medium",
          isDarkTheme ? "text-stone-300" : "text-stone-600"
        )}>
          {message}
        </p>
      </div>
    </div>
  );
}
