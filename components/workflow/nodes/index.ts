import { nodeRegistry } from '@/lib/workflow/node-registry';
import { NodeRegistration } from '@/lib/workflow/node-interface';
import {
  TextIcon,
  Hash,
  Upload,
  BarChart,
  Globe,
  Code,
  FilterIcon,
  Database,
  Image as ImageIcon,
  MessageSquare,
  Table as TableIcon,
  Sparkles,
  RefreshCw,
  WandIcon,
  Package,
  RotateCcw,
  Clock
} from 'lucide-react';

// Import execution definitions
import { textInputExecution } from './text-input-node';
import { sampleNewExecution } from './sample-new-node';
import { numberInputExecution } from './number-input-node';
import { textOutputExecution } from './text-output-node';
import { fileInputExecution } from './file-input-node';
import { converterExecution } from './converter-node';
import { nlpExecution } from './nlp-node';
import { loopExecution } from './loop-node';
import { schedulerExecution } from './scheduler-node';

/**
 * Register all basic input nodes (loaded immediately)
 */
const basicNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'textInput',
      label: 'Text Input',
      description: 'Input text data manually',
      icon: TextIcon,
      category: 'input',
      needsOnChangeHandler: true,
      isDynamic: false
    },
    loader: () => import('./text-input-node'),
    execution: textInputExecution
  },
  {
    metadata: {
      type: 'numberInput',
      label: 'Number Input',
      description: 'Input numeric data',
      icon: Hash,
      category: 'input',
      needsOnChangeHandler: true,
      isDynamic: false
    },
    loader: () => import('./number-input-node'),
    execution: numberInputExecution
  },
  {
    metadata: {
      type: 'textOutput',
      label: 'Text Output',
      description: 'Display text data',
      icon: TextIcon,
      category: 'output',
      needsOnChangeHandler: false,
      isDynamic: false
    },
    loader: () => import('./text-output-node'),
    execution: textOutputExecution
  },
  {
    metadata: {
      type: 'converter',
      label: 'Converter',
      description: 'Convert between data formats',
      icon: RefreshCw,
      category: 'transform',
      needsOnChangeHandler: true,
      isDynamic: false
    },
    loader: () => import('./converter-node'),
    execution: converterExecution
  },
  {
    metadata: {
      type: 'installed-node',
      label: 'Installed Node',
      description: 'Node from marketplace',
      icon: Package,
      category: 'advanced',
      needsOnChangeHandler: true,
      isDynamic: false
    },
    loader: () => import('./installed-node'),
    execution: {
      inputs: [
        {
          id: 'input',
          name: 'Input',
          type: 'string',
          required: false,
          description: 'Input data for the installed node'
        }
      ],
      outputs: [
        {
          id: 'output',
          name: 'Output',
          type: 'string',
          description: 'Output data from the installed node'
        }
      ],
      execute: async (inputs, config, context) => {
        try {
          // Get the node definition from the context
          const nodeData = context.nodeData;
          const nodeDefinition = nodeData?.nodeDefinition;

          if (!nodeDefinition || !nodeDefinition.execute) {
            // Fallback execution for basic nodes
            return {
              output: `Processed: ${inputs.input || 'No input'} (Basic execution)`
            };
          }

          // Execute using the node definition's execute function
          const result = await nodeDefinition.execute(inputs, config);
          return result;

        } catch (error) {
          context.log(`Execution error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');
          throw error;
        }
      }
    }
  }
];

/**
 * Register advanced nodes (loaded dynamically)
 */
const advancedNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'fileInput',
      label: 'File Input',
      description: 'Upload and read files',
      icon: Upload,
      category: 'input',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./file-input-node'),
    execution: fileInputExecution
  },
  {
    metadata: {
      type: 'chartOutput',
      label: 'Chart Output',
      description: 'Display data as charts',
      icon: BarChart,
      category: 'output',
      needsOnChangeHandler: false,
      isDynamic: true
    },
    loader: () => import('./chart-output-node')
  },
  {
    metadata: {
      type: 'tableOutput',
      label: 'Table Output',
      description: 'Display data in table format',
      icon: TableIcon,
      category: 'output',
      needsOnChangeHandler: false,
      isDynamic: true
    },
    loader: () => import('./table-output-node')
  },
  {
    metadata: {
      type: 'transform',
      label: 'Transform',
      description: 'Transform and manipulate data',
      icon: WandIcon,
      category: 'transform',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./transform-node')
  },
  {
    metadata: {
      type: 'filter',
      label: 'Filter',
      description: 'Filter data based on conditions',
      icon: FilterIcon,
      category: 'transform',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./filter-node')
  },
  {
    metadata: {
      type: 'mathOperation',
      label: 'Math Operation',
      description: 'Perform mathematical operations',
      icon: Hash,
      category: 'transform',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./math-operation-node')
  },
  {
    metadata: {
      type: 'apiRequest',
      label: 'API Request',
      description: 'Make HTTP API requests',
      icon: Globe,
      category: 'advanced',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./api-request-node')
  },
  {
    metadata: {
      type: 'codeExecution',
      label: 'Code Execution',
      description: 'Execute custom JavaScript code',
      icon: Code,
      category: 'advanced',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./code-execution-node')
  }
];

/**
 * Register data and AI nodes
 */
const dataAndAiNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'databaseQuery',
      label: 'Database Query',
      description: 'Query databases with SQL',
      icon: Database,
      category: 'data',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./database-query-node')
  },
  {
    metadata: {
      type: 'imageProcessing',
      label: 'Image Processing',
      description: 'Process and analyze images',
      icon: ImageIcon,
      category: 'ai',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./image-processing-node')
  },
  {
    metadata: {
      type: 'nlp',
      label: 'NLP Analysis',
      description: 'Natural language processing',
      icon: MessageSquare,
      category: 'ai',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./nlp-node'),
    execution: nlpExecution
  },
  {
    metadata: {
      type: 'generateText',
      label: 'Generate Text',
      description: 'Generate text using AI models',
      icon: Sparkles,
      category: 'ai',
      needsOnChangeHandler: true,
      isDynamic: true
    },
    loader: () => import('./generate-text-node')
  }
];

/**
 * Control flow nodes - loops, scheduling, and automation
 */
const controlFlowNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'loop',
      label: 'Loop Control',
      description: 'Execute iterations with count, while, or foreach loops',
      icon: RotateCcw,
      category: 'control',
      needsOnChangeHandler: true,
      isDynamic: true,
      tags: ['loop', 'iteration', 'control', 'automation']
    },
    loader: () => import('./loop-node'),
    execution: loopExecution
  },
  {
    metadata: {
      type: 'scheduler',
      label: 'Scheduler',
      description: 'Schedule workflow execution with intervals, cron, or one-time',
      icon: Clock,
      category: 'control',
      needsOnChangeHandler: true,
      isDynamic: true,
      tags: ['schedule', 'automation', 'timer', 'cron']
    },
    loader: () => import('./scheduler-node'),
    execution: schedulerExecution
  }
];

/**
 * Example/Demo nodes - showing how easy it is to add new nodes
 */
const exampleNodes: NodeRegistration[] = [
  {
    metadata: {
      type: 'sampleNew',
      label: 'Sample Node',
      description: 'Example of new modular node architecture',
      icon: Sparkles,
      category: 'advanced',
      needsOnChangeHandler: true,
      isDynamic: true,
      tags: ['example', 'demo', 'sample']
    },
    loader: () => import('./sample-new-node'),
    execution: sampleNewExecution
  }
];

/**
 * Register all nodes with the registry
 */
export function registerAllNodes(): void {
  console.log('Registering workflow nodes...');

  // Register basic nodes
  basicNodes.forEach(node => nodeRegistry.registerNode(node));

  // Register advanced nodes
  advancedNodes.forEach(node => nodeRegistry.registerNode(node));

  // Register data and AI nodes
  dataAndAiNodes.forEach(node => nodeRegistry.registerNode(node));

  // Register control flow nodes
  controlFlowNodes.forEach(node => nodeRegistry.registerNode(node));

  // Register example nodes
  exampleNodes.forEach(node => nodeRegistry.registerNode(node));

  const stats = nodeRegistry.getStats();
  console.log('Node registration complete:', stats);
}

/**
 * Get all registered nodes (for compatibility)
 */
export function getAllRegisteredNodes() {
  return nodeRegistry.getAllNodes();
}

/**
 * Export the registry for direct access
 */
export { nodeRegistry };
