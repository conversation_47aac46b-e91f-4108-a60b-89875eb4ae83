"use client";

import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "reactflow";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Package,
  Play,
  Square,
  Settings,
  Info,
  AlertCircle,
  CheckCircle,
  Loader2
} from "lucide-react";
import { NodeDefinition } from "@/lib/node-loader";
import { NodeLoader } from "@/lib/node-loader";
import { nodeAnalytics } from "@/lib/node-analytics";
import { useSession } from "next-auth/react";

interface InstalledNodeData {
  label: string;
  description: string;
  nodeDefinition: NodeDefinition;
  nodeType: string;
  version: string;
  category: string;
  icon?: string;
  inputs: any[];
  outputs: any[];
  originalNodeId?: string;
  value?: string;
  inputValue?: string;
  fileContent?: string;
  onChange?: (value: string) => void;
  onRawDataChange?: (value: string) => void;
  onJsonChange?: (value: string) => void;
}

interface InstalledNodeProps extends NodeProps {
  data: InstalledNodeData;
}

export default function InstalledNode({ data, selected }: InstalledNodeProps) {
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionStatus, setExecutionStatus] = useState<'idle' | 'running' | 'success' | 'error'>('idle');
  const [output, setOutput] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [inputValues, setInputValues] = useState<Record<string, any>>({});
  const [showSettings, setShowSettings] = useState(false);
  const nodeLoaderRef = useRef<NodeLoader | null>(null);
  const { data: session } = useSession();

  // Initialize node loader
  useEffect(() => {
    nodeLoaderRef.current = NodeLoader.getInstance();
  }, []);

  // Initialize input values based on node definition
  useEffect(() => {
    if (data.nodeDefinition?.inputs) {
      const initialValues: Record<string, any> = {};
      data.nodeDefinition.inputs.forEach(input => {
        initialValues[input.name] = input.defaultValue || '';
      });
      setInputValues(initialValues);
    }
  }, [data.nodeDefinition]);

  const handleInputChange = (inputName: string, value: any) => {
    try {
      setInputValues(prev => ({
        ...prev,
        [inputName]: value
      }));

      // Also call the onChange handler if available
      if (data.onChange && typeof data.onChange === 'function') {
        data.onChange(String(value));
      }
    } catch (error) {
      console.error('Error handling input change:', error);
      setError('Failed to update input value');
    }
  };

  const executeNode = async () => {
    if (!data.nodeDefinition || !nodeLoaderRef.current || !session?.user) {
      setError('Node definition, loader, or user session not available');
      return;
    }

    const startTime = performance.now();
    setIsExecuting(true);
    setExecutionStatus('running');
    setError('');
    setOutput('');

    try {
      // Load the node code and execute it
      const nodeCode = await fetch(`/api/nodes/code/${data.nodeDefinition.id}`);
      if (!nodeCode.ok) {
        throw new Error('Failed to load node code');
      }

      const codeData = await nodeCode.json();

      // TODO: Implement proper node loading from code
      // For now, simulate execution
      const result = `Executed node ${data.nodeDefinition.id} with inputs: ${JSON.stringify(inputValues)}`;
      const endTime = performance.now();
      const executionTime = Math.round(endTime - startTime);

      setOutput(typeof result === 'string' ? result : JSON.stringify(result, null, 2));
      setExecutionStatus('success');

      // Record analytics
      if (session?.user?.email) {
        await nodeAnalytics.recordExecution({
          nodeId: data.nodeDefinition.id,
          userId: session.user.email, // Use email as user identifier
          executionTime,
          success: true,
          inputSize: JSON.stringify(inputValues).length,
          outputSize: typeof result === 'string' ? result.length : JSON.stringify(result).length,
          timestamp: new Date()
        });
      }

      // Trigger onChange callbacks if available
      if (data.onChange) {
        data.onChange(typeof result === 'string' ? result : JSON.stringify(result));
      }

    } catch (err) {
      const endTime = performance.now();
      const executionTime = Math.round(endTime - startTime);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';

      setError(errorMessage);
      setExecutionStatus('error');
      console.error('Node execution error:', err);

      // Record failed execution analytics
      if (session?.user?.email) {
        await nodeAnalytics.recordExecution({
          nodeId: data.nodeDefinition.id,
          userId: session.user.email, // Use email as user identifier
          executionTime,
          success: false,
          errorType: errorMessage,
          inputSize: JSON.stringify(inputValues).length,
          timestamp: new Date()
        });
      }
    } finally {
      setIsExecuting(false);
    }
  };

  const stopExecution = () => {
    setIsExecuting(false);
    setExecutionStatus('idle');
  };

  const getStatusIcon = () => {
    switch (executionStatus) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const getStatusColor = () => {
    switch (executionStatus) {
      case 'running':
        return 'border-blue-500';
      case 'success':
        return 'border-green-500';
      case 'error':
        return 'border-red-500';
      default:
        return selected ? 'border-primary' : 'border-border';
    }
  };

  return (
    <Card className={`min-w-[280px] max-w-[350px] transition-all duration-200 ${getStatusColor()}`}>
      {/* Input Handles */}
      {data.inputs && data.inputs.length > 0 ? (
        data.inputs.map((input, index) => (
          <Handle
            key={`input-${index}`}
            type="target"
            position={Position.Left}
            id={input.id || input.name || `input-${index}`}
            style={{
              top: `${((index + 1) / (data.inputs.length + 1)) * 100}%`,
              background: '#666',
              width: 12,
              height: 12,
            }}
          />
        ))
      ) : (
        <Handle
          type="target"
          position={Position.Left}
          id="input"
          style={{
            top: '50%',
            background: '#666',
            width: 12,
            height: 12,
          }}
        />
      )}

      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <span className="truncate">{data.label}</span>
              {!data.nodeDefinition?.component && (
                <Badge variant="outline" className="text-xs text-orange-600 border-orange-600">
                  Basic
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground font-normal mt-1">
              {data.description}
            </p>
          </div>
          <div className="flex gap-1">
            <Button
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-3 w-3" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Input Fields */}
        {showSettings && data.inputs.length > 0 && (
          <div className="space-y-2">
            {data.inputs.map((input) => (
              <div key={input.name} className="space-y-1">
                <Label className="text-xs">{input.label || input.name}</Label>
                {input.type === 'string' || input.type === 'text' ? (
                  input.multiline ? (
                    <Textarea
                      placeholder={input.description}
                      value={inputValues[input.name] || ''}
                      onChange={(e) => handleInputChange(input.name, e.target.value)}
                      className="text-xs"
                      rows={2}
                    />
                  ) : (
                    <Input
                      placeholder={input.description}
                      value={inputValues[input.name] || ''}
                      onChange={(e) => handleInputChange(input.name, e.target.value)}
                      className="text-xs"
                    />
                  )
                ) : input.type === 'number' ? (
                  <Input
                    type="number"
                    placeholder={input.description}
                    value={inputValues[input.name] || ''}
                    onChange={(e) => handleInputChange(input.name, parseFloat(e.target.value) || 0)}
                    className="text-xs"
                  />
                ) : (
                  <Input
                    placeholder={input.description}
                    value={inputValues[input.name] || ''}
                    onChange={(e) => handleInputChange(input.name, e.target.value)}
                    className="text-xs"
                  />
                )}
              </div>
            ))}
          </div>
        )}

        {/* Execution Controls */}
        <div className="flex gap-2">
          <Button
            size="sm"
            onClick={executeNode}
            disabled={isExecuting}
            className="flex-1"
          >
            {isExecuting ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Running
              </>
            ) : (
              <>
                <Play className="h-3 w-3 mr-1" />
                Execute
              </>
            )}
          </Button>
          {isExecuting && (
            <Button
              size="sm"
              variant="outline"
              onClick={stopExecution}
            >
              <Square className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Output Display */}
        {(output || error) && (
          <div className="space-y-1">
            <Label className="text-xs font-medium">Output</Label>
            <Textarea
              value={error || output}
              readOnly
              className={`text-xs font-mono ${error ? 'text-red-500' : ''}`}
              rows={3}
            />
          </div>
        )}

        {/* Status Badge */}
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="text-xs">
            {data.category}
          </Badge>
          {executionStatus !== 'idle' && (
            <Badge
              variant={executionStatus === 'error' ? 'destructive' : 'default'}
              className="text-xs"
            >
              {executionStatus}
            </Badge>
          )}
        </div>
      </CardContent>

      {/* Output Handles */}
      {data.outputs && data.outputs.length > 0 ? (
        data.outputs.map((output, index) => (
          <Handle
            key={`output-${index}`}
            type="source"
            position={Position.Right}
            id={output.id || output.name || `output-${index}`}
            style={{
              top: `${((index + 1) / (data.outputs.length + 1)) * 100}%`,
              background: '#666',
              width: 12,
              height: 12,
            }}
          />
        ))
      ) : (
        <Handle
          type="source"
          position={Position.Right}
          id="output"
          style={{
            top: '50%',
            background: '#666',
            width: 12,
            height: 12,
          }}
        />
      )}
    </Card>
  );
}
