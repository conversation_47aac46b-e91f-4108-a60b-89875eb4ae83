"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { PlusIcon, Package, Zap } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { nodeRegistry } from "@/lib/workflow/node-registry";
import { NodeCategory } from "@/lib/workflow/node-interface";
import { useInstalledNodes } from "@/hooks/use-installed-nodes";
import { NodeDefinition } from "@/lib/node-loader";

type NodeSelectorProps = {
  onAddNode: (nodeType: string, nodeDefinition?: NodeDefinition) => void;
};

export default function NodeSelector({ onAddNode }: NodeSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [nodeCategories, setNodeCategories] = useState<Array<{
    title: string;
    nodes: Array<{
      type: string;
      label: string;
      description: string;
      icon: any;
    }>;
  }>>([]);
  const ref = useRef<HTMLDivElement>(null);

  // Get installed nodes from the marketplace
  const {
    nodeDefinitions: installedNodes,
    loading: installedNodesLoading,
    error: installedNodesError
  } = useInstalledNodes();

  // Debug logging
  useEffect(() => {
    console.log('[NodeSelector] Installed nodes:', {
      count: installedNodes.length,
      loading: installedNodesLoading,
      error: installedNodesError,
      nodes: installedNodes.map(n => ({ id: n.id, name: n.name, type: n.type, hasComponent: !!n.component, icon: n.icon }))
    });
  }, [installedNodes, installedNodesLoading, installedNodesError]);

  // Helper function to check if icon is a URL
  const isIconUrl = (icon: string): boolean => {
    const isUrl = icon.startsWith('http://') ||
           icon.startsWith('https://') ||
           icon.startsWith('/') ||
           icon.startsWith('data:image/') ||
           icon.includes('.png') ||
           icon.includes('.jpg') ||
           icon.includes('.jpeg') ||
           icon.includes('.gif') ||
           icon.includes('.svg') ||
           icon.includes('.webp');

    // Debug logging for icon detection
    console.log(`[NodeSelector] Icon detection: "${icon}" -> ${isUrl ? 'URL' : 'emoji/text'}`);
    return isUrl;
  };

  // Load node categories from registry
  useEffect(() => {
    const loadCategories = () => {
      console.log('[NodeSelector] Loading categories from registry...');
      const categories = nodeRegistry.getCategories();
      const allNodes = nodeRegistry.getAllNodes();
      console.log('[NodeSelector] Total nodes in registry:', allNodes.length);

      const categoryOrder: NodeCategory[] = ['input', 'output', 'transform', 'control', 'advanced', 'ai', 'data'];

      const organizedCategories = categoryOrder.map(categoryKey => {
        const categoryInfo = categories[categoryKey];
        const nodes = nodeRegistry.getNodesByCategory(categoryKey);

        console.log(`[NodeSelector] Category "${categoryKey}":`, nodes.length, 'nodes');

        return {
          title: categoryInfo?.title || categoryKey.charAt(0).toUpperCase() + categoryKey.slice(1),
          nodes: nodes.map(node => ({
            type: node.type,
            label: node.label,
            description: node.description,
            icon: node.icon
          }))
        };
      }).filter(category => category.nodes.length > 0); // Only show categories with nodes

      console.log('[NodeSelector] Organized categories:', organizedCategories);
      setNodeCategories(organizedCategories);
    };

    // Load categories initially
    loadCategories();

    // Also listen for registry updates (in case nodes are registered later)
    const handleRegistryUpdate = () => {
      console.log('[NodeSelector] Registry updated, reloading categories...');
      loadCategories();
    };

    // Add event listener for registry updates
    window.addEventListener('nodeRegistryUpdated', handleRegistryUpdate);

    return () => {
      window.removeEventListener('nodeRegistryUpdated', handleRegistryUpdate);
    };
  }, []);

  // Close the selector when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div ref={ref}>
      {isOpen ? (
        <Card className="w-[400px] absolute top-0 left-0 z-20 shadow-lg transition-colors">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <PlusIcon className="h-4 w-4" />
              Add Node
            </CardTitle>
          </CardHeader>
          <CardContent className="p-3">
            <Tabs defaultValue="builtin" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="builtin" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Built-in
                </TabsTrigger>
                <TabsTrigger value="installed" className="flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Installed
                  {installedNodes.length > 0 && (
                    <Badge variant="secondary" className="ml-1 text-xs">
                      {installedNodes.length}
                    </Badge>
                  )}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="builtin" className="mt-3">
                <ScrollArea className="h-[400px] pr-3">
                  {nodeCategories.map((category) => (
                    <div key={category.title} className="mb-4">
                      <h3 className="text-sm font-medium mb-2">{category.title}</h3>
                      <div className="grid gap-2">
                        {category.nodes.map((node) => (
                          <Button
                            key={node.type}
                            variant="outline"
                            className="justify-start h-auto py-2"
                            onClick={() => {
                              onAddNode(node.type);
                              setIsOpen(false);
                            }}
                          >
                            <node.icon className="mr-2 h-4 w-4 flex-shrink-0" />
                            <div className="flex flex-col items-start text-left">
                              <span>{node.label}</span>
                              <span className="text-xs text-muted-foreground">{node.description}</span>
                            </div>
                          </Button>
                        ))}
                      </div>
                    </div>
                  ))}
                </ScrollArea>
              </TabsContent>

              <TabsContent value="installed" className="mt-3">
                <ScrollArea className="h-[400px] pr-3">
                  {installedNodesLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : installedNodesError ? (
                    <div className="text-center py-8">
                      <Package className="h-8 w-8 mx-auto mb-2 opacity-50 text-red-500" />
                      <p className="text-sm text-muted-foreground mb-2">
                        Failed to load installed nodes
                      </p>
                      <p className="text-xs text-muted-foreground mb-3">
                        {installedNodesError}
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.location.reload()}
                        className="text-xs"
                      >
                        Retry
                      </Button>
                    </div>
                  ) : installedNodes.length === 0 ? (
                    <div className="text-center py-8">
                      <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm text-muted-foreground mb-2">
                        No nodes installed
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Visit the marketplace to install nodes
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {installedNodes.map((node) => (
                        <Button
                          key={node.id}
                          variant="outline"
                          className="justify-start h-auto py-3 w-full"
                          onClick={() => {
                            onAddNode(node.type, node);
                            setIsOpen(false);
                          }}
                        >
                          <div className="flex items-start gap-3 w-full">
                            <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded flex items-center justify-center">
                              {node.icon ? (
                                isIconUrl(node.icon) ? (
                                  <img
                                    src={node.icon}
                                    alt={node.name}
                                    className="w-6 h-6 rounded object-cover"
                                    onError={(e) => {
                                      // Fallback to package icon if image fails to load
                                      e.currentTarget.style.display = 'none';
                                      const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                                      if (fallback) fallback.classList.remove('hidden');
                                    }}
                                  />
                                ) : (
                                  <span className="text-sm">{node.icon}</span>
                                )
                              ) : (
                                <Package className="h-4 w-4" />
                              )}
                              <Package className="h-4 w-4 hidden" />
                            </div>
                            <div className="flex-1 min-w-0 text-left">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium text-sm truncate">
                                  {node.name}
                                </span>
                                <Badge variant="outline" className="text-xs">
                                  v{node.version}
                                </Badge>
                                {!node.component && (
                                  <Badge variant="outline" className="text-xs text-orange-600 border-orange-600">
                                    Basic
                                  </Badge>
                                )}
                              </div>
                              <p className="text-xs text-muted-foreground line-clamp-2">
                                {node.description}
                              </p>
                              <div className="flex items-center gap-1 mt-1">
                                <Badge variant="secondary" className="text-xs">
                                  {node.category}
                                </Badge>
                                {node.inputs.length > 0 && (
                                  <Badge variant="outline" className="text-xs">
                                    {node.inputs.length} inputs
                                  </Badge>
                                )}
                                {node.outputs.length > 0 && (
                                  <Badge variant="outline" className="text-xs">
                                    {node.outputs.length} outputs
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      ) : (
        <Button
          size="sm"
          variant="outline"
          onClick={() => setIsOpen(true)}
          className="gap-2 bg-background transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          Add Node
          {installedNodes.length > 0 && (
            <Badge variant="secondary" className="ml-1 text-xs">
              {installedNodes.length + nodeCategories.reduce((acc, cat) => acc + cat.nodes.length, 0)}
            </Badge>
          )}
        </Button>
      )}
    </div>
  );
}
