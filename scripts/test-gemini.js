// Test script to verify Google Gemini API integration
// This is a simple test to check if the Google Gemini API endpoint is accessible

async function testGeminiAPI() {
  const testApiKey = "AIza-test-key"; // Replace with actual API key for testing
  const testPrompt = "Hello, how are you?";
  const model = "gemini-1.5-flash"; // or "gemini-1.5-pro"

  try {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${testApiKey}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: testPrompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 100,
          topP: 0.8,
          topK: 10
        }
      })
    });

    console.log("Response status:", response.status);
    console.log("Response headers:", Object.fromEntries(response.headers.entries()));

    const data = await response.json();
    console.log("Response data:", JSON.stringify(data, null, 2));

    if (response.ok) {
      if (data.candidates && data.candidates.length > 0 && data.candidates[0].content) {
        console.log("✅ Google Gemini API integration successful!");
        console.log("Generated text:", data.candidates[0].content.parts[0].text);
      } else {
        console.log("❌ No content generated by Gemini");
        console.log("Response structure:", data);
      }
    } else {
      console.log("❌ Google Gemini API error:", data.error?.message || data.message);
    }
  } catch (error) {
    console.error("❌ Network error:", error.message);
  }
}

// Test function to check available models
async function testGeminiModels() {
  const testApiKey = "AIza-test-key"; // Replace with actual API key for testing

  try {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${testApiKey}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json"
      }
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log("✅ Available Gemini models:");
      data.models?.forEach(model => {
        if (model.name.includes('gemini')) {
          console.log(`- ${model.name}: ${model.displayName}`);
        }
      });
    } else {
      console.log("❌ Error fetching models:", data.error?.message);
    }
  } catch (error) {
    console.error("❌ Network error:", error.message);
  }
}

// Uncomment the lines below to run the tests (requires valid API key)
// testGeminiAPI();
// testGeminiModels();

console.log("Google Gemini API test script loaded. Set a valid API key and uncomment the function calls to test.");
