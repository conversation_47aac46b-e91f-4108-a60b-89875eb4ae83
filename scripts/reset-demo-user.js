// Import PrismaClient directly
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');

const prisma = new PrismaClient();

async function resetDemoUser() {
  try {
    console.log('Deleting existing demo user...');
    
    // Delete the existing demo user if it exists
    await prisma.user.deleteMany({
      where: {
        email: '<EMAIL>',
      },
    });
    
    console.log('Demo user deleted successfully');
    
    // Create a new demo user
    console.log('Creating new demo user...');
    
    // Hash password
    const hashedPassword = await bcrypt.hash('password123', 10);
    
    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    
    // Create new user with email already verified
    const newUser = await prisma.user.create({
      data: {
        username: 'demo',
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'De<PERSON> User',
        verificationToken,
        emailVerified: true, // Set email as verified by default
      },
    });
    
    console.log('Demo user created successfully:', {
      id: newUser.id,
      username: newU<PERSON>.username,
      email: newUser.email,
      name: newUser.name,
      emailVerified: newUser.emailVerified,
    });
    
    console.log('Demo user reset completed successfully');
  } catch (error) {
    console.error('Error resetting demo user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
resetDemoUser();
