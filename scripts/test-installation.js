// Test script to verify the plug & play installation system
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testInstallation() {
  try {
    console.log('🧪 Testing Plug & Play Installation System...\n');

    // 1. Check if we have nodes with code
    console.log('📦 Checking available nodes with code...');
    const nodesWithCode = await prisma.nodePlugin.findMany({
      include: {
        nodeCodes: true,
        author: {
          select: {
            name: true,
            email: true
          }
        }
      },
      where: {
        nodeCodes: {
          some: {}
        }
      }
    });

    console.log(`Found ${nodesWithCode.length} nodes with code:`);
    nodesWithCode.forEach(node => {
      console.log(`  - ${node.name} (${node.tier}) - ${node.nodeCodes.length} code versions`);
    });

    if (nodesWithCode.length === 0) {
      console.log('❌ No nodes with code found. Please run the seed scripts first.');
      return;
    }

    // 2. Check if we have a test user
    console.log('\n👤 Checking for test user...');
    const testUser = await prisma.user.findFirst({
      where: {
        email: {
          contains: '@'
        }
      }
    });

    if (!testUser) {
      console.log('❌ No test user found. Please create a user account first.');
      return;
    }

    console.log(`Found test user: ${testUser.name} (${testUser.email})`);

    // 3. Check current installations
    console.log('\n📋 Checking current installations...');
    const currentInstallations = await prisma.installedNode.findMany({
      where: {
        userId: testUser.id
      },
      include: {
        node: {
          select: {
            name: true,
            tier: true
          }
        }
      }
    });

    console.log(`Current installations: ${currentInstallations.length}`);
    currentInstallations.forEach(installation => {
      console.log(`  - ${installation.node.name} (${installation.status})`);
    });

    // 4. Test installation of a free node
    console.log('\n🔧 Testing installation of a free node...');
    const freeNode = nodesWithCode.find(node => node.tier === 'free');
    
    if (freeNode) {
      // Check if already installed
      const existingInstallation = currentInstallations.find(i => i.nodeId === freeNode.id);
      
      if (existingInstallation) {
        console.log(`✅ Node "${freeNode.name}" is already installed with status: ${existingInstallation.status}`);
      } else {
        // Simulate installation
        console.log(`Installing "${freeNode.name}"...`);
        
        const installation = await prisma.installedNode.create({
          data: {
            userId: testUser.id,
            nodeId: freeNode.id,
            version: freeNode.nodeCodes[0].version,
            status: 'installed',
            installPath: `/nodes/${testUser.id}/${freeNode.id}`,
            config: {},
            enabled: true
          }
        });

        console.log(`✅ Successfully installed "${freeNode.name}" (ID: ${installation.id})`);
      }
    } else {
      console.log('⚠️  No free nodes available for testing');
    }

    // 5. Test API endpoints
    console.log('\n🌐 Testing API endpoints...');
    
    // Test getting installed nodes
    const installedNodesAfter = await prisma.installedNode.findMany({
      where: {
        userId: testUser.id
      },
      include: {
        node: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true
              }
            }
          }
        }
      }
    });

    console.log(`✅ Found ${installedNodesAfter.length} installed nodes for user`);

    // Test getting node code
    if (installedNodesAfter.length > 0) {
      const testInstallation = installedNodesAfter[0];
      const nodeCode = await prisma.nodeCode.findFirst({
        where: {
          nodeId: testInstallation.nodeId
        }
      });

      if (nodeCode) {
        console.log(`✅ Found node code for "${testInstallation.node.name}" (${nodeCode.code.length} characters)`);
        console.log(`   Dependencies: ${JSON.stringify(nodeCode.dependencies)}`);
        console.log(`   Permissions: ${JSON.stringify(nodeCode.permissions)}`);
      } else {
        console.log(`❌ No code found for "${testInstallation.node.name}"`);
      }
    }

    console.log('\n🎉 Plug & Play Installation System Test Complete!');
    console.log('\n📊 Summary:');
    console.log(`   - Nodes with code: ${nodesWithCode.length}`);
    console.log(`   - User installations: ${installedNodesAfter.length}`);
    console.log(`   - System status: ✅ Ready for testing`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testInstallation();
