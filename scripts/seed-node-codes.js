const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const prisma = new PrismaClient();

async function seedNodeCodes() {
  try {
    console.log('🌱 Seeding node codes...');

    // Read the text processor node code
    const textProcessorPath = path.join(process.cwd(), 'lib/sample-nodes/text-processor-node.js');
    const textProcessorCode = fs.readFileSync(textProcessorPath, 'utf-8');
    const textProcessorChecksum = crypto.createHash('sha256').update(textProcessorCode).digest('hex');

    // Find existing nodes to add code to
    const nodes = await prisma.nodePlugin.findMany({
      where: {
        name: {
          in: ['Advanced CSV Processor', 'OpenAI GPT Node', 'Webhook Listener']
        }
      }
    });

    if (nodes.length === 0) {
      console.log('⚠️  No matching nodes found. Please run the main seed script first.');
      return;
    }

    // Add code for CSV Processor node (we'll use this as our text processor)
    const textProcessorNode = nodes.find(n => n.name === 'Advanced CSV Processor');
    if (textProcessorNode) {
      await prisma.nodeCode.upsert({
        where: {
          nodeId_version: {
            nodeId: textProcessorNode.id,
            version: '1.0.0'
          }
        },
        create: {
          nodeId: textProcessorNode.id,
          version: '1.0.0',
          code: textProcessorCode,
          dependencies: {
            react: '^18.0.0',
            reactflow: '^11.0.0'
          },
          permissions: {
            network: false,
            filesystem: false,
            storage: true
          },
          checksum: textProcessorChecksum
        },
        update: {
          code: textProcessorCode,
          checksum: textProcessorChecksum,
          dependencies: {
            react: '^18.0.0',
            reactflow: '^11.0.0'
          }
        }
      });
      console.log('✅ Added code for CSV Processor node');
    }

    // Create sample code for OpenAI GPT node
    const dataTransformerNode = nodes.find(n => n.name === 'OpenAI GPT Node');
    if (dataTransformerNode) {
      const dataTransformerCode = `
// Data Transformer Node
const React = require('react');
const { Handle, Position } = require('reactflow');

const DataTransformerComponent = ({ data, selected }) => {
  return React.createElement('div', {
    className: \`bg-white border-2 rounded-lg p-4 shadow-sm min-w-[200px] \${
      selected ? 'border-green-500' : 'border-gray-200'
    }\`
  }, [
    React.createElement(Handle, {
      key: 'input',
      type: 'target',
      position: Position.Left,
      style: { background: '#555' }
    }),
    React.createElement('div', {
      key: 'header',
      className: 'flex items-center gap-2 mb-3'
    }, [
      React.createElement('div', {
        key: 'icon',
        className: 'w-8 h-8 bg-green-100 rounded flex items-center justify-center'
      }, '🔄'),
      React.createElement('div', { key: 'title' }, [
        React.createElement('h3', {
          key: 'name',
          className: 'font-semibold text-sm'
        }, 'Data Transformer'),
        React.createElement('p', {
          key: 'desc',
          className: 'text-xs text-gray-500'
        }, 'Transform data formats')
      ])
    ]),
    React.createElement(Handle, {
      key: 'output',
      type: 'source',
      position: Position.Right,
      style: { background: '#555' }
    })
  ]);
};

const executeDataTransformer = async (inputs, config) => {
  const { data } = inputs;
  const { format = 'json' } = config;

  // Transform data based on format
  let result;
  switch (format) {
    case 'json':
      result = JSON.stringify(data, null, 2);
      break;
    case 'csv':
      if (Array.isArray(data)) {
        const headers = Object.keys(data[0] || {});
        const csv = [headers.join(','), ...data.map(row => headers.map(h => row[h]).join(','))].join('\\n');
        result = csv;
      } else {
        result = JSON.stringify(data);
      }
      break;
    default:
      result = data;
  }

  return { transformedData: result, format };
};

const nodeDefinition = {
  id: 'data-transformer',
  name: 'Data Transformer',
  type: 'data-transformer',
  category: 'data',
  description: 'Transform data between different formats',
  icon: '🔄',
  version: '1.0.0',
  inputs: [{ id: 'data', name: 'Data', type: 'object', required: true }],
  outputs: [{ id: 'transformedData', name: 'Transformed Data', type: 'string' }],
  config: [
    {
      id: 'format',
      name: 'Output Format',
      type: 'select',
      required: true,
      defaultValue: 'json',
      options: [
        { label: 'JSON', value: 'json' },
        { label: 'CSV', value: 'csv' }
      ]
    }
  ],
  component: DataTransformerComponent,
  execute: executeDataTransformer
};
      `;

      const dataTransformerChecksum = crypto.createHash('sha256').update(dataTransformerCode).digest('hex');

      await prisma.nodeCode.upsert({
        where: {
          nodeId_version: {
            nodeId: dataTransformerNode.id,
            version: '1.0.0'
          }
        },
        create: {
          nodeId: dataTransformerNode.id,
          version: '1.0.0',
          code: dataTransformerCode,
          dependencies: {
            react: '^18.0.0',
            reactflow: '^11.0.0'
          },
          permissions: {
            network: false,
            filesystem: false,
            storage: true
          },
          checksum: dataTransformerChecksum
        },
        update: {
          code: dataTransformerCode,
          checksum: dataTransformerChecksum
        }
      });
      console.log('✅ Added code for OpenAI GPT node');
    }

    console.log('🎉 Node codes seeded successfully!');

  } catch (error) {
    console.error('❌ Error seeding node codes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
seedNodeCodes();
