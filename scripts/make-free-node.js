const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function makeFreeNode() {
  try {
    const node = await prisma.nodePlugin.findFirst({
      where: { name: 'Advanced CSV Processor' }
    });
    
    if (node) {
      await prisma.nodePlugin.update({
        where: { id: node.id },
        data: { tier: 'free' }
      });
      console.log('✅ Updated Advanced CSV Processor to free tier');
    } else {
      console.log('❌ Node not found');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

makeFreeNode();
