import { PrismaClient } from '@prisma/client';
import { SYSTEM_ROLES, SYSTEM_PERMISSIONS } from '../lib/user-management/permissions';

const prisma = new PrismaClient();

async function initializeDeveloperRoles() {
  console.log('🚀 Initializing developer roles and permissions...');

  try {
    // Create permissions if they don't exist
    console.log('📝 Creating permissions...');
    for (const permission of SYSTEM_PERMISSIONS) {
      await prisma.permission.upsert({
        where: {
          resource_action: {
            resource: permission.resource,
            action: permission.action
          }
        },
        update: {
          displayName: permission.displayName,
          description: permission.description
        },
        create: {
          resource: permission.resource,
          action: permission.action,
          displayName: permission.displayName,
          description: permission.description
        }
      });
    }
    console.log('✅ Permissions created/updated');

    // Create roles if they don't exist
    console.log('👥 Creating roles...');
    for (const roleData of SYSTEM_ROLES) {
      // Create or update the role
      const role = await prisma.role.upsert({
        where: { name: roleData.name },
        update: {
          displayName: roleData.displayName,
          description: roleData.description
        },
        create: {
          name: roleData.name,
          displayName: roleData.displayName,
          description: roleData.description
        }
      });

      // Clear existing permissions for this role
      await prisma.rolePermission.deleteMany({
        where: { roleId: role.id }
      });

      // Add permissions to the role
      for (const permissionString of roleData.permissions) {
        const [resource, action] = permissionString.split(':');
        
        const permission = await prisma.permission.findUnique({
          where: {
            resource_action: {
              resource,
              action
            }
          }
        });

        if (permission) {
          await prisma.rolePermission.create({
            data: {
              roleId: role.id,
              permissionId: permission.id
            }
          });
        } else {
          console.warn(`⚠️  Permission not found: ${permissionString}`);
        }
      }

      console.log(`✅ Role "${roleData.displayName}" created/updated with ${roleData.permissions.length} permissions`);
    }

    // Update existing admin users to have node approval permissions
    console.log('🔧 Updating admin permissions...');
    const adminRole = await prisma.role.findUnique({
      where: { name: 'admin' },
      include: { permissions: true }
    });

    if (adminRole) {
      console.log(`✅ Admin role has ${adminRole.permissions.length} permissions including node approval`);
    }

    // Check if there are any super admins
    const superAdmins = await prisma.user.findMany({
      where: { isSuperAdmin: true },
      select: { id: true, name: true, email: true }
    });

    console.log(`👑 Found ${superAdmins.length} super admin(s):`);
    superAdmins.forEach(admin => {
      console.log(`   - ${admin.name} (${admin.email})`);
    });

    // Check if there are any regular admins
    const admins = await prisma.user.findMany({
      where: { isAdmin: true, isSuperAdmin: false },
      select: { id: true, name: true, email: true }
    });

    console.log(`🛡️  Found ${admins.length} admin(s):`);
    admins.forEach(admin => {
      console.log(`   - ${admin.name} (${admin.email})`);
    });

    // Show developer role info
    const developerRole = await prisma.role.findUnique({
      where: { name: 'developer' },
      include: { 
        permissions: true,
        userRoles: {
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        }
      }
    });

    if (developerRole) {
      console.log(`🔧 Developer role created with ${developerRole.permissions.length} permissions`);
      console.log(`👨‍💻 ${developerRole.userRoles.length} user(s) have developer role:`);
      developerRole.userRoles.forEach(userRole => {
        console.log(`   - ${userRole.user.name} (${userRole.user.email})`);
      });
    }

    console.log('\n🎉 Developer roles and permissions initialization complete!');
    console.log('\n📋 Summary:');
    console.log(`   - ${SYSTEM_PERMISSIONS.length} permissions created/updated`);
    console.log(`   - ${SYSTEM_ROLES.length} roles created/updated`);
    console.log(`   - ${superAdmins.length} super admin(s) can approve nodes`);
    console.log(`   - ${admins.length} admin(s) can approve nodes`);
    console.log(`   - Developer role available for assignment`);

    console.log('\n🔧 Next steps:');
    console.log('   1. Assign "developer" role to users who should upload nodes');
    console.log('   2. Super admins and admins can access /admin/nodes for approval');
    console.log('   3. Developers can access /developer/nodes/status to track submissions');

  } catch (error) {
    console.error('❌ Error initializing developer roles:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the initialization
if (require.main === module) {
  initializeDeveloperRoles()
    .then(() => {
      console.log('✅ Initialization completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Initialization failed:', error);
      process.exit(1);
    });
}

export default initializeDeveloperRoles;
