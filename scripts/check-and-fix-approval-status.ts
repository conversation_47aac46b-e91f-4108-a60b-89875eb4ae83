import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkAndFixApprovalStatus() {
  try {
    console.log('🔍 Checking current node approval statuses...\n');

    // Get all nodes with their current approval status
    const allNodes = await prisma.nodePlugin.findMany({
      select: {
        id: true,
        name: true,
        approvalStatus: true,
        verified: true,
        featured: true,
        author: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`Found ${allNodes.length} nodes in database:\n`);

    // Display current status
    allNodes.forEach((node, index) => {
      console.log(`${index + 1}. ${node.name}`);
      console.log(`   ID: ${node.id}`);
      console.log(`   Approval Status: ${node.approvalStatus}`);
      console.log(`   Verified: ${node.verified}`);
      console.log(`   Featured: ${node.featured}`);
      console.log(`   Author: ${node.author?.name || node.author?.email || 'Unknown'}`);
      console.log('');
    });

    // Get stats
    const stats = await prisma.nodePlugin.groupBy({
      by: ['approvalStatus'],
      _count: {
        approvalStatus: true
      }
    });

    console.log('📊 Current approval status distribution:');
    stats.forEach(stat => {
      console.log(`   ${stat.approvalStatus}: ${stat._count.approvalStatus} nodes`);
    });

    // Fix nodes that should be approved (verified or featured nodes)
    console.log('\n🔧 Fixing approval status for verified/featured nodes...');

    const nodesToApprove = allNodes.filter(node => 
      (node.verified || node.featured) && node.approvalStatus === 'pending'
    );

    if (nodesToApprove.length > 0) {
      console.log(`\nFound ${nodesToApprove.length} nodes that should be approved:`);
      
      for (const node of nodesToApprove) {
        console.log(`   - Approving: ${node.name}`);
        
        await prisma.nodePlugin.update({
          where: { id: node.id },
          data: {
            approvalStatus: 'approved',
            approvedAt: new Date()
          }
        });
      }
      
      console.log('\n✅ Updated approval status for verified/featured nodes');
    } else {
      console.log('\n✅ No nodes need approval status updates');
    }

    // Create some test nodes with different statuses for testing
    console.log('\n🧪 Creating test nodes with different approval statuses...');

    // Check if test user exists
    let testUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'test-developer',
          name: 'Test Developer',
          bio: 'Test developer for approval system',
          avatar: '/api/placeholder/64/64',
          emailVerified: true
        }
      });
      console.log('   ✅ Created test developer user');
    }

    // Create test nodes with different statuses
    const testNodes = [
      {
        id: 'test-pending-node',
        name: 'Test Pending Node',
        description: 'A test node in pending status',
        approvalStatus: 'pending'
      },
      {
        id: 'test-approved-node',
        name: 'Test Approved Node',
        description: 'A test node in approved status',
        approvalStatus: 'approved'
      },
      {
        id: 'test-rejected-node',
        name: 'Test Rejected Node',
        description: 'A test node in rejected status',
        approvalStatus: 'rejected'
      }
    ];

    for (const testNode of testNodes) {
      const existing = await prisma.nodePlugin.findUnique({
        where: { id: testNode.id }
      });

      if (!existing) {
        await prisma.nodePlugin.create({
          data: {
            id: testNode.id,
            name: testNode.name,
            version: '1.0.0',
            description: testNode.description,
            longDescription: `Long description for ${testNode.name}`,
            authorId: testUser.id,
            category: 'utility',
            tier: 'free',
            price: 0,
            tags: JSON.stringify(['test']),
            dependencies: JSON.stringify([]),
            permissions: JSON.stringify([]),
            icon: '/api/placeholder/64/64',
            screenshots: JSON.stringify([]),
            downloadUrl: `/api/nodes/${testNode.id}/download`,
            compatibility: JSON.stringify({ minVersion: "1.0.0" }),
            approvalStatus: testNode.approvalStatus,
            approvedAt: testNode.approvalStatus === 'approved' ? new Date() : null,
            rejectionReason: testNode.approvalStatus === 'rejected' ? 'Test rejection reason' : null
          }
        });

        // Create node code
        await prisma.nodeCode.create({
          data: {
            nodeId: testNode.id,
            version: '1.0.0',
            code: `// Test node code for ${testNode.name}`,
            dependencies: [],
            permissions: [],
            checksum: 'test-checksum'
          }
        });

        console.log(`   ✅ Created test node: ${testNode.name} (${testNode.approvalStatus})`);
      } else {
        console.log(`   ⏭️  Test node ${testNode.name} already exists`);
      }
    }

    // Final stats
    console.log('\n📊 Final approval status distribution:');
    const finalStats = await prisma.nodePlugin.groupBy({
      by: ['approvalStatus'],
      _count: {
        approvalStatus: true
      }
    });

    finalStats.forEach(stat => {
      console.log(`   ${stat.approvalStatus}: ${stat._count.approvalStatus} nodes`);
    });

    console.log('\n🎉 Database check and fix completed!');

  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

checkAndFixApprovalStatus();
