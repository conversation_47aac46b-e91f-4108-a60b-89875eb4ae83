const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedNodes() {
  console.log('🌱 Seeding sample marketplace nodes...');

  try {
    // Create sample author
    let author = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!author) {
      author = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'sample-developer',
          name: '<PERSON>ple Developer',
          bio: 'Sample developer for testing marketplace nodes',
          avatar: '/api/placeholder/64/64',
          emailVerified: true
        }
      });
      console.log('✅ Created sample author user');
    }

    // Sample nodes data
    const nodes = [
      {
        id: 'text-processor-pro',
        name: 'Text Processor Pro',
        version: '1.2.0',
        description: 'Advanced text processing with regex, formatting, and validation',
        longDescription: 'A powerful text processing node that supports regular expressions, text formatting, validation, and transformation.',
        category: 'text_processing',
        tier: 'free',
        price: 0,
        tags: JSON.stringify(['text', 'regex', 'formatting']),
        dependencies: JSON.stringify([]),
        permissions: JSON.stringify(['text-processing']),
        icon: '/api/placeholder/64/64',
        screenshots: JSON.stringify(['/api/placeholder/400/300']),
        downloadUrl: '/api/nodes/text-processor-pro/download',
        verified: true,
        featured: true,
        rating: 4.8,
        reviewCount: 156,
        downloads: 2847,
        weeklyDownloads: 89,
        compatibility: JSON.stringify({ minVersion: "1.0.0" }),
        changelog: JSON.stringify([{ version: '1.2.0', date: new Date(), changes: ['Initial release'] }]),
        code: `
function executeNode(env) {
  const { inputs } = env;
  const { text, operation } = inputs;
  
  let result = text || '';
  
  switch (operation) {
    case 'uppercase':
      result = result.toUpperCase();
      break;
    case 'lowercase':
      result = result.toLowerCase();
      break;
    case 'trim':
      result = result.trim();
      break;
    case 'word_count':
      result = result.split(/\\s+/).filter(word => word.length > 0).length;
      break;
    default:
      result = text;
  }
  
  return {
    processedText: result,
    originalLength: (text || '').length,
    processedLength: result.toString().length,
    timestamp: new Date().toISOString()
  };
}
        `
      },
      {
        id: 'data-validator',
        name: 'Data Validator',
        version: '2.1.0',
        description: 'Validate data formats, types, and constraints',
        longDescription: 'Comprehensive data validation node supporting email, URL, phone number, date, and custom validation rules.',
        category: 'data_processing',
        tier: 'free',
        price: 0,
        tags: JSON.stringify(['validation', 'data-quality']),
        dependencies: JSON.stringify([]),
        permissions: JSON.stringify(['data-validation']),
        icon: '/api/placeholder/64/64',
        screenshots: JSON.stringify(['/api/placeholder/400/300']),
        downloadUrl: '/api/nodes/data-validator/download',
        verified: true,
        featured: true,
        rating: 4.6,
        reviewCount: 89,
        downloads: 1456,
        weeklyDownloads: 45,
        compatibility: JSON.stringify({ minVersion: "1.0.0" }),
        changelog: JSON.stringify([{ version: '2.1.0', date: new Date(), changes: ['Initial release'] }]),
        code: `
function executeNode(env) {
  const { inputs } = env;
  const { data, validationType } = inputs;
  
  let isValid = false;
  let errorMessage = '';
  
  try {
    switch (validationType) {
      case 'email':
        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
        isValid = emailRegex.test(data);
        if (!isValid) errorMessage = 'Invalid email format';
        break;
      case 'url':
        try {
          new URL(data);
          isValid = true;
        } catch {
          isValid = false;
          errorMessage = 'Invalid URL format';
        }
        break;
      default:
        isValid = data && data.length > 0;
        if (!isValid) errorMessage = 'Data is empty or null';
    }
  } catch (error) {
    isValid = false;
    errorMessage = error.message;
  }
  
  return {
    isValid,
    errorMessage,
    validatedData: isValid ? data : null,
    validationType,
    timestamp: new Date().toISOString()
  };
}
        `
      },
      {
        id: 'http-request',
        name: 'HTTP Request',
        version: '3.0.1',
        description: 'Make HTTP requests with full customization',
        longDescription: 'Professional HTTP client node supporting all HTTP methods, custom headers, authentication, and response processing.',
        category: 'api_integration',
        tier: 'free',
        price: 0,
        tags: JSON.stringify(['http', 'api', 'request']),
        dependencies: JSON.stringify([]),
        permissions: JSON.stringify(['network-access']),
        icon: '/api/placeholder/64/64',
        screenshots: JSON.stringify(['/api/placeholder/400/300']),
        downloadUrl: '/api/nodes/http-request/download',
        verified: true,
        featured: true,
        rating: 4.7,
        reviewCount: 234,
        downloads: 5678,
        weeklyDownloads: 156,
        compatibility: JSON.stringify({ minVersion: "1.0.0" }),
        changelog: JSON.stringify([{ version: '3.0.1', date: new Date(), changes: ['Initial release'] }]),
        code: `
async function executeNode(env) {
  const { inputs } = env;
  const { url, method, body } = inputs;
  
  try {
    const response = await fetch(url, {
      method: method || 'GET',
      body: body && ['POST', 'PUT', 'PATCH'].includes(method) ? body : undefined,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.text();
    
    return {
      success: response.ok,
      status: response.status,
      data: data,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}
        `
      }
    ];

    // Create nodes
    for (const nodeData of nodes) {
      const existingNode = await prisma.nodePlugin.findUnique({
        where: { id: nodeData.id }
      });

      if (existingNode) {
        console.log(`⏭️  Node ${nodeData.name} already exists, skipping...`);
        continue;
      }

      // Create the node
      const { code, ...nodeCreateData } = nodeData;
      const node = await prisma.nodePlugin.create({
        data: {
          ...nodeCreateData,
          authorId: author.id
        }
      });

      // Create the node code
      await prisma.nodeCode.create({
        data: {
          nodeId: nodeData.id,
          version: nodeData.version,
          code: nodeData.code,
          dependencies: nodeData.dependencies,
          permissions: nodeData.permissions,
          checksum: Buffer.from(nodeData.code).toString('base64').slice(0, 32)
        }
      });

      console.log(`✅ Created node: ${nodeData.name} (${nodeData.id})`);
    }

    console.log('🎉 Sample nodes seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding nodes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedNodes();
