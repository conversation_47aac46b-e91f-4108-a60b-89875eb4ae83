import { PrismaClient } from '@prisma/client';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';

const prisma = new PrismaClient();

async function createTestNodes() {
  try {
    console.log('🧪 Creating test nodes for delete functionality...\n');

    // Find or create test user
    let testUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'test-developer',
          name: 'Test Developer',
          bio: 'Test developer for node deletion testing',
          avatar: '/api/placeholder/64/64',
          emailVerified: true
        }
      });
      console.log('✅ Created test developer user');
    }

    // Create test nodes with different statuses
    const testNodes = [
      {
        id: 'test-delete-pending',
        name: 'Test Delete Pending Node',
        description: 'A test node in pending status for deletion testing',
        approvalStatus: 'pending'
      },
      {
        id: 'test-delete-approved',
        name: 'Test Delete Approved Node',
        description: 'A test node in approved status for deletion testing',
        approvalStatus: 'approved'
      },
      {
        id: 'test-delete-rejected',
        name: 'Test Delete Rejected Node',
        description: 'A test node in rejected status for deletion testing',
        approvalStatus: 'rejected'
      }
    ];

    for (const testNode of testNodes) {
      // Check if node already exists
      const existing = await prisma.nodePlugin.findUnique({
        where: { id: testNode.id }
      });

      if (existing) {
        console.log(`⏭️  Test node ${testNode.name} already exists, skipping...`);
        continue;
      }

      // Create the node
      const node = await prisma.nodePlugin.create({
        data: {
          id: testNode.id,
          name: testNode.name,
          version: '1.0.0',
          description: testNode.description,
          longDescription: `Long description for ${testNode.name}. This node is created for testing the delete functionality.`,
          authorId: testUser.id,
          category: 'utility',
          tier: 'free',
          price: 0,
          tags: JSON.stringify(['test', 'delete', 'utility']),
          dependencies: JSON.stringify([]),
          permissions: JSON.stringify(['basic']),
          icon: '/api/placeholder/64/64',
          screenshots: JSON.stringify(['/api/placeholder/400/300']),
          downloadUrl: `/api/nodes/${testNode.id}/download`,
          compatibility: JSON.stringify({ minVersion: "1.0.0" }),
          approvalStatus: testNode.approvalStatus,
          approvedAt: testNode.approvalStatus === 'approved' ? new Date() : null,
          rejectionReason: testNode.approvalStatus === 'rejected' ? 'Test rejection for deletion testing' : null
        }
      });

      // Create node code
      const nodeCode = `
// Test node code for ${testNode.name}
const nodeDefinition = {
  id: '${testNode.id}',
  name: '${testNode.name}',
  version: '1.0.0',
  description: '${testNode.description}',
  category: 'utility',
  inputs: [
    {
      id: 'input',
      name: 'Input',
      type: 'string',
      required: true
    }
  ],
  outputs: [
    {
      id: 'output',
      name: 'Output',
      type: 'string'
    }
  ],
  execute: async (inputs) => {
    return {
      output: 'Test output from ' + inputs.input
    };
  }
};
`;

      await prisma.nodeCode.create({
        data: {
          nodeId: testNode.id,
          version: '1.0.0',
          code: nodeCode,
          dependencies: [],
          permissions: ['basic'],
          checksum: 'test-checksum-' + testNode.id
        }
      });

      // Create test files
      const uploadDir = join(process.cwd(), 'uploads', 'nodes', testUser.id, testNode.id);
      await mkdir(uploadDir, { recursive: true });
      
      const packagePath = join(uploadDir, `${testNode.name.replace(/\s+/g, '-')}-1.0.0.zip`);
      await writeFile(packagePath, `Test package content for ${testNode.name}`);

      console.log(`✅ Created test node: ${testNode.name} (${testNode.approvalStatus})`);
    }

    // Display final stats
    console.log('\n📊 Test nodes created successfully!');
    const stats = await prisma.nodePlugin.groupBy({
      by: ['approvalStatus'],
      where: {
        authorId: testUser.id
      },
      _count: {
        approvalStatus: true
      }
    });

    console.log('\nTest user node distribution:');
    stats.forEach(stat => {
      console.log(`   ${stat.approvalStatus}: ${stat._count.approvalStatus} nodes`);
    });

    console.log('\n🎯 You can now test the delete functionality at:');
    console.log('   http://localhost:3000/developer/nodes/status');

  } catch (error) {
    console.error('❌ Error creating test nodes:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createTestNodes();
