// Test script to verify DeepSeek API integration
// This is a simple test to check if the DeepSeek API endpoint is accessible

async function testDeepSeekAPI() {
  const testApiKey = "sk-test-key"; // Replace with actual API key for testing
  const testPrompt = "Hello, how are you?";

  try {
    const response = await fetch("https://api.deepseek.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `<PERSON><PERSON> ${testApiKey}`
      },
      body: JSON.stringify({
        model: "deepseek-chat",
        messages: [{ role: "user", content: testPrompt }],
        temperature: 0.7,
        max_tokens: 100,
        stream: false
      })
    });

    console.log("Response status:", response.status);
    console.log("Response headers:", Object.fromEntries(response.headers.entries()));

    const data = await response.json();
    console.log("Response data:", data);

    if (response.ok) {
      console.log("✅ DeepSeek API integration successful!");
      console.log("Generated text:", data.choices[0].message.content);
    } else {
      console.log("❌ DeepSeek API error:", data.error?.message || data.message);
    }
  } catch (error) {
    console.error("❌ Network error:", error.message);
  }
}

// Uncomment the line below to run the test (requires valid API key)
// testDeepSeekAPI();

console.log("DeepSeek API test script loaded. Set a valid API key and uncomment the function call to test.");
