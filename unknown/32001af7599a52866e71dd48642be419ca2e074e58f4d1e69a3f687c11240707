"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Trash2, 
  Settings, 
  Download, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  Package,
  TrendingUp,
  Store
} from "lucide-react";
import { InstalledNode, InstallationStatus } from "@/lib/marketplace/types";
import { pluginRegistry } from "@/lib/marketplace/plugin-registry";
import { Marketplace } from "./marketplace";

interface NodeManagerProps {
  onNodeToggle?: (nodeId: string, enabled: boolean) => void;
  onNodeUninstall?: (nodeId: string) => void;
}

export function NodeManager({ onNodeToggle, onNodeUninstall }: NodeManagerProps) {
  const [installedNodes, setInstalledNodes] = useState<InstalledNode[]>([]);
  const [availableUpdates, setAvailableUpdates] = useState<InstalledNode[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('installed');

  useEffect(() => {
    loadInstalledNodes();
    
    // Listen for plugin registry changes
    const handlePluginChange = () => {
      loadInstalledNodes();
    };

    pluginRegistry.on('plugin:registered', handlePluginChange);
    pluginRegistry.on('plugin:unregistered', handlePluginChange);
    pluginRegistry.on('plugin:toggled', handlePluginChange);

    return () => {
      pluginRegistry.off('plugin:registered', handlePluginChange);
      pluginRegistry.off('plugin:unregistered', handlePluginChange);
      pluginRegistry.off('plugin:toggled', handlePluginChange);
    };
  }, []);

  const loadInstalledNodes = () => {
    try {
      setLoading(true);
      const installed = pluginRegistry.getInstalledPlugins();
      setInstalledNodes(installed);
      
      // Filter nodes that have updates available
      const updates = installed.filter(node => 
        node.installationStatus === InstallationStatus.UPDATE_AVAILABLE
      );
      setAvailableUpdates(updates);
    } catch (error) {
      console.error('Failed to load installed nodes:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleNode = async (nodeId: string, enabled: boolean) => {
    try {
      await pluginRegistry.togglePlugin(nodeId, enabled);
      onNodeToggle?.(nodeId, enabled);
      loadInstalledNodes();
    } catch (error) {
      console.error('Failed to toggle node:', error);
    }
  };

  const handleUninstallNode = async (nodeId: string) => {
    try {
      await pluginRegistry.unregisterPlugin(nodeId);
      onNodeUninstall?.(nodeId);
      loadInstalledNodes();
    } catch (error) {
      console.error('Failed to uninstall node:', error);
    }
  };

  const getStatusIcon = (status: InstallationStatus) => {
    switch (status) {
      case InstallationStatus.INSTALLED:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case InstallationStatus.UPDATE_AVAILABLE:
        return <Download className="h-4 w-4 text-blue-500" />;
      case InstallationStatus.ERROR:
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case InstallationStatus.DISABLED:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
      default:
        return <Package className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusText = (status: InstallationStatus) => {
    switch (status) {
      case InstallationStatus.INSTALLED:
        return 'Installed';
      case InstallationStatus.UPDATE_AVAILABLE:
        return 'Update Available';
      case InstallationStatus.ERROR:
        return 'Error';
      case InstallationStatus.DISABLED:
        return 'Disabled';
      default:
        return 'Unknown';
    }
  };

  const InstalledNodeCard = ({ node }: { node: InstalledNode }) => (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          <img 
            src={node.icon} 
            alt={node.name}
            className="w-10 h-10 rounded-md object-cover"
          />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold truncate">{node.name}</h3>
              <Badge variant="outline" className="text-xs">
                v{node.installedVersion}
              </Badge>
              {getStatusIcon(node.installationStatus)}
            </div>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Avatar className="w-4 h-4">
                <AvatarImage src={node.author.avatar} />
                <AvatarFallback className="text-xs">
                  {node.author.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <span>{node.author.name}</span>
              <span>•</span>
              <span>{getStatusText(node.installationStatus)}</span>
            </div>
            <p className="text-sm text-muted-foreground truncate mt-1">
              {node.description}
            </p>
            {node.usageCount > 0 && (
              <div className="flex items-center gap-1 mt-2 text-xs text-muted-foreground">
                <TrendingUp className="h-3 w-3" />
                <span>Used {node.usageCount} times</span>
                {node.lastUsed && (
                  <span>• Last used {new Date(node.lastUsed).toLocaleDateString()}</span>
                )}
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Switch
              checked={node.enabled}
              onCheckedChange={(enabled) => handleToggleNode(node.id, enabled)}
              disabled={node.installationStatus === InstallationStatus.ERROR}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleUninstallNode(node.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const stats = pluginRegistry.getPluginStats();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Node Manager</h1>
        <p className="text-muted-foreground">
          Manage your installed nodes and discover new ones
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Nodes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Enabled</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.enabled}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Disabled</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-muted-foreground">{stats.disabled}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Updates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{availableUpdates.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="installed" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Installed ({installedNodes.length})
          </TabsTrigger>
          <TabsTrigger value="updates" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Updates ({availableUpdates.length})
          </TabsTrigger>
          <TabsTrigger value="marketplace" className="flex items-center gap-2">
            <Store className="h-4 w-4" />
            Marketplace
          </TabsTrigger>
        </TabsList>

        <TabsContent value="installed" className="space-y-4">
          {installedNodes.length > 0 ? (
            <div className="space-y-3">
              {installedNodes.map((node) => (
                <InstalledNodeCard key={node.id} node={node} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Package className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No nodes installed</h3>
              <p className="text-muted-foreground mb-4">
                Browse the marketplace to discover and install nodes
              </p>
              <Button onClick={() => setActiveTab('marketplace')}>
                Browse Marketplace
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="updates" className="space-y-4">
          {availableUpdates.length > 0 ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  {availableUpdates.length} update{availableUpdates.length !== 1 ? 's' : ''} available
                </p>
                <Button size="sm">Update All</Button>
              </div>
              {availableUpdates.map((node) => (
                <InstalledNodeCard key={node.id} node={node} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500 opacity-50" />
              <h3 className="text-lg font-semibold mb-2">All nodes are up to date</h3>
              <p className="text-muted-foreground">
                Your installed nodes are running the latest versions
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="marketplace">
          <Marketplace />
        </TabsContent>
      </Tabs>
    </div>
  );
}
