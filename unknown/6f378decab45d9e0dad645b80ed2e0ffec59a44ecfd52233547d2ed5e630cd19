import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { nodeLoader, NodeDefinition } from '@/lib/node-loader';

export interface InstalledNode {
  id: string;
  nodeId: string;
  version: string;
  status: 'installing' | 'installed' | 'failed' | 'updating' | 'uninstalling';
  enabled: boolean;
  config: any;
  installedAt: string;
  updatedAt: string;
  lastUsed?: string;
  node: {
    id: string;
    name: string;
    description: string;
    icon: string;
    category: string;
    tier: string;
    version: string;
    tags: string[];
    author: {
      id: string;
      name: string;
      email: string;
      avatar?: string;
    };
    verified: boolean;
    rating: number;
    downloads: number;
  };
}

export function useInstalledNodes() {
  const { data: session, status } = useSession();
  const [installedNodes, setInstalledNodes] = useState<InstalledNode[]>([]);
  const [nodeDefinitions, setNodeDefinitions] = useState<NodeDefinition[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchInstalledNodes = useCallback(async () => {
    // Don't fetch if not authenticated
    if (!session) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/nodes/installed');
      if (!response.ok) {
        // Handle authentication errors gracefully
        if (response.status === 401) {
          setError('Authentication required');
          setLoading(false);
          return;
        }
        throw new Error('Failed to fetch installed nodes');
      }

      const { installedNodes: nodes } = await response.json();
      setInstalledNodes(nodes);

      // Load node definitions for enabled nodes
      const definitions: NodeDefinition[] = [];
      for (const node of nodes) {
        if (node.enabled && node.status === 'installed') {
          try {
            const definition = await nodeLoader.loadNode(node.nodeId, node.version);
            if (definition) {
              definitions.push(definition);
            }
          } catch (error) {
            console.warn(`Failed to load definition for node ${node.nodeId}:`, error);
          }
        }
      }
      setNodeDefinitions(definitions);

    } catch (error) {
      console.error('Failed to fetch installed nodes:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [session]);

  const installNode = useCallback(async (nodeId: string, version?: string): Promise<boolean> => {
    try {
      const success = await nodeLoader.installNode(nodeId, version);
      if (success) {
        await fetchInstalledNodes(); // Refresh the list
      }
      return success;
    } catch (error) {
      console.error('Failed to install node:', error);
      setError(error instanceof Error ? error.message : 'Installation failed');
      return false;
    }
  }, [fetchInstalledNodes]);

  const uninstallNode = useCallback(async (nodeId: string): Promise<boolean> => {
    try {
      const success = await nodeLoader.uninstallNode(nodeId);
      if (success) {
        await fetchInstalledNodes(); // Refresh the list
      }
      return success;
    } catch (error) {
      console.error('Failed to uninstall node:', error);
      setError(error instanceof Error ? error.message : 'Uninstallation failed');
      return false;
    }
  }, [fetchInstalledNodes]);

  const toggleNode = useCallback(async (nodeId: string, enabled: boolean): Promise<boolean> => {
    try {
      const response = await fetch('/api/nodes/installed', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodeId, enabled }),
      });

      if (!response.ok) {
        throw new Error('Failed to toggle node');
      }

      await fetchInstalledNodes(); // Refresh the list
      return true;
    } catch (error) {
      console.error('Failed to toggle node:', error);
      setError(error instanceof Error ? error.message : 'Toggle failed');
      return false;
    }
  }, [fetchInstalledNodes]);

  const updateNodeConfig = useCallback(async (nodeId: string, config: any): Promise<boolean> => {
    try {
      const response = await fetch('/api/nodes/installed', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodeId, config }),
      });

      if (!response.ok) {
        throw new Error('Failed to update node config');
      }

      await fetchInstalledNodes(); // Refresh the list
      return true;
    } catch (error) {
      console.error('Failed to update node config:', error);
      setError(error instanceof Error ? error.message : 'Config update failed');
      return false;
    }
  }, [fetchInstalledNodes]);

  const getNodeDefinition = useCallback((nodeId: string): NodeDefinition | null => {
    return nodeDefinitions.find(def => def.id === nodeId) || null;
  }, [nodeDefinitions]);

  const isNodeInstalled = useCallback((nodeId: string): boolean => {
    return installedNodes.some(node =>
      node.nodeId === nodeId &&
      node.status === 'installed'
      // Don't check enabled status - installed is installed regardless of enabled state
    );
  }, [installedNodes]);

  const getInstalledNode = useCallback((nodeId: string): InstalledNode | null => {
    return installedNodes.find(node => node.nodeId === nodeId) || null;
  }, [installedNodes]);

  useEffect(() => {
    if (status !== 'loading') {
      fetchInstalledNodes();
    }
  }, [fetchInstalledNodes, status]);

  return {
    installedNodes,
    nodeDefinitions,
    loading,
    error,
    fetchInstalledNodes,
    installNode,
    uninstallNode,
    toggleNode,
    updateNodeConfig,
    getNodeDefinition,
    isNodeInstalled,
    getInstalledNode,
  };
}
