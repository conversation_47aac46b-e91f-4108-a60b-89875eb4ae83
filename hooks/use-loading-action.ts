"use client";

import { useCallback } from "react";
import { useLoading } from "@/context/loading-context";

/**
 * A hook that wraps an async function with loading state
 * 
 * @example
 * const { runWithLoading } = useLoadingAction();
 * 
 * // Then in your component:
 * const handleSubmit = async () => {
 *   await runWithLoading(async () => {
 *     // Your async code here
 *     await someAsyncFunction();
 *   }, "Saving data...");
 * };
 */
export function useLoadingAction() {
  const { startLoading, stopLoading } = useLoading();
  
  /**
   * Runs an async function with loading state
   * 
   * @param action The async function to run
   * @param loadingMessage Optional message to display during loading
   * @returns The result of the async function
   */
  const runWithLoading = useCallback(
    async <T,>(action: () => Promise<T>, loadingMessage?: string): Promise<T> => {
      try {
        startLoading(loadingMessage);
        return await action();
      } finally {
        stopLoading();
      }
    },
    [startLoading, stopLoading]
  );
  
  return { runWithLoading };
}
